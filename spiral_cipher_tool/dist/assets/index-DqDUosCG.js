(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();function $h(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var af={exports:{}},xo={},uf={exports:{}},F={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xr=Symbol.for("react.element"),Wh=Symbol.for("react.portal"),Hh=Symbol.for("react.fragment"),Gh=Symbol.for("react.strict_mode"),Kh=Symbol.for("react.profiler"),Qh=Symbol.for("react.provider"),Xh=Symbol.for("react.context"),Yh=Symbol.for("react.forward_ref"),Zh=Symbol.for("react.suspense"),Jh=Symbol.for("react.memo"),qh=Symbol.for("react.lazy"),Ya=Symbol.iterator;function bh(e){return e===null||typeof e!="object"?null:(e=Ya&&e[Ya]||e["@@iterator"],typeof e=="function"?e:null)}var cf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ff=Object.assign,df={};function Zn(e,t,n){this.props=e,this.context=t,this.refs=df,this.updater=n||cf}Zn.prototype.isReactComponent={};Zn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Zn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function pf(){}pf.prototype=Zn.prototype;function Dl(e,t,n){this.props=e,this.context=t,this.refs=df,this.updater=n||cf}var _l=Dl.prototype=new pf;_l.constructor=Dl;ff(_l,Zn.prototype);_l.isPureReactComponent=!0;var Za=Array.isArray,hf=Object.prototype.hasOwnProperty,Nl={current:null},mf={key:!0,ref:!0,__self:!0,__source:!0};function gf(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)hf.call(t,r)&&!mf.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:Xr,type:e,key:o,ref:s,props:i,_owner:Nl.current}}function em(e,t){return{$$typeof:Xr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Fl(e){return typeof e=="object"&&e!==null&&e.$$typeof===Xr}function tm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Ja=/\/+/g;function Wo(e,t){return typeof e=="object"&&e!==null&&e.key!=null?tm(""+e.key):t.toString(36)}function Ti(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Xr:case Wh:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+Wo(s,0):r,Za(i)?(n="",e!=null&&(n=e.replace(Ja,"$&/")+"/"),Ti(i,t,n,"",function(u){return u})):i!=null&&(Fl(i)&&(i=em(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(Ja,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",Za(e))for(var l=0;l<e.length;l++){o=e[l];var a=r+Wo(o,l);s+=Ti(o,t,n,a,i)}else if(a=bh(e),typeof a=="function")for(e=a.call(e),l=0;!(o=e.next()).done;)o=o.value,a=r+Wo(o,l++),s+=Ti(o,t,n,a,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function ii(e,t,n){if(e==null)return e;var r=[],i=0;return Ti(e,r,"","",function(o){return t.call(n,o,i++)}),r}function nm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var we={current:null},Ei={transition:null},rm={ReactCurrentDispatcher:we,ReactCurrentBatchConfig:Ei,ReactCurrentOwner:Nl};function yf(){throw Error("act(...) is not supported in production builds of React.")}F.Children={map:ii,forEach:function(e,t,n){ii(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ii(e,function(){t++}),t},toArray:function(e){return ii(e,function(t){return t})||[]},only:function(e){if(!Fl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};F.Component=Zn;F.Fragment=Hh;F.Profiler=Kh;F.PureComponent=Dl;F.StrictMode=Gh;F.Suspense=Zh;F.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=rm;F.act=yf;F.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=ff({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=Nl.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)hf.call(t,a)&&!mf.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:Xr,type:e.type,key:i,ref:o,props:r,_owner:s}};F.createContext=function(e){return e={$$typeof:Xh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Qh,_context:e},e.Consumer=e};F.createElement=gf;F.createFactory=function(e){var t=gf.bind(null,e);return t.type=e,t};F.createRef=function(){return{current:null}};F.forwardRef=function(e){return{$$typeof:Yh,render:e}};F.isValidElement=Fl;F.lazy=function(e){return{$$typeof:qh,_payload:{_status:-1,_result:e},_init:nm}};F.memo=function(e,t){return{$$typeof:Jh,type:e,compare:t===void 0?null:t}};F.startTransition=function(e){var t=Ei.transition;Ei.transition={};try{e()}finally{Ei.transition=t}};F.unstable_act=yf;F.useCallback=function(e,t){return we.current.useCallback(e,t)};F.useContext=function(e){return we.current.useContext(e)};F.useDebugValue=function(){};F.useDeferredValue=function(e){return we.current.useDeferredValue(e)};F.useEffect=function(e,t){return we.current.useEffect(e,t)};F.useId=function(){return we.current.useId()};F.useImperativeHandle=function(e,t,n){return we.current.useImperativeHandle(e,t,n)};F.useInsertionEffect=function(e,t){return we.current.useInsertionEffect(e,t)};F.useLayoutEffect=function(e,t){return we.current.useLayoutEffect(e,t)};F.useMemo=function(e,t){return we.current.useMemo(e,t)};F.useReducer=function(e,t,n){return we.current.useReducer(e,t,n)};F.useRef=function(e){return we.current.useRef(e)};F.useState=function(e){return we.current.useState(e)};F.useSyncExternalStore=function(e,t,n){return we.current.useSyncExternalStore(e,t,n)};F.useTransition=function(){return we.current.useTransition()};F.version="18.3.1";uf.exports=F;var A=uf.exports;const Ol=$h(A);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var im=A,om=Symbol.for("react.element"),sm=Symbol.for("react.fragment"),lm=Object.prototype.hasOwnProperty,am=im.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,um={key:!0,ref:!0,__self:!0,__source:!0};function vf(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)lm.call(t,r)&&!um.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:om,type:e,key:o,ref:s,props:i,_owner:am.current}}xo.Fragment=sm;xo.jsx=vf;xo.jsxs=vf;af.exports=xo;var w=af.exports,Ls={},xf={exports:{}},_e={},wf={exports:{}},Sf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(L,R){var N=L.length;L.push(R);e:for(;0<N;){var _=N-1>>>1,W=L[_];if(0<i(W,R))L[_]=R,L[N]=W,N=_;else break e}}function n(L){return L.length===0?null:L[0]}function r(L){if(L.length===0)return null;var R=L[0],N=L.pop();if(N!==R){L[0]=N;e:for(var _=0,W=L.length,Kt=W>>>1;_<Kt;){var qe=2*(_+1)-1,yn=L[qe],Me=qe+1,Qt=L[Me];if(0>i(yn,N))Me<W&&0>i(Qt,yn)?(L[_]=Qt,L[Me]=N,_=Me):(L[_]=yn,L[qe]=N,_=qe);else if(Me<W&&0>i(Qt,N))L[_]=Qt,L[Me]=N,_=Me;else break e}}return R}function i(L,R){var N=L.sortIndex-R.sortIndex;return N!==0?N:L.id-R.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],u=[],c=1,f=null,d=3,g=!1,y=!1,v=!1,S=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function h(L){for(var R=n(u);R!==null;){if(R.callback===null)r(u);else if(R.startTime<=L)r(u),R.sortIndex=R.expirationTime,t(a,R);else break;R=n(u)}}function x(L){if(v=!1,h(L),!y)if(n(a)!==null)y=!0,Z(k);else{var R=n(u);R!==null&&Fe(x,R.startTime-L)}}function k(L,R){y=!1,v&&(v=!1,m(P),P=-1),g=!0;var N=d;try{for(h(R),f=n(a);f!==null&&(!(f.expirationTime>R)||L&&!re());){var _=f.callback;if(typeof _=="function"){f.callback=null,d=f.priorityLevel;var W=_(f.expirationTime<=R);R=e.unstable_now(),typeof W=="function"?f.callback=W:f===n(a)&&r(a),h(R)}else r(a);f=n(a)}if(f!==null)var Kt=!0;else{var qe=n(u);qe!==null&&Fe(x,qe.startTime-R),Kt=!1}return Kt}finally{f=null,d=N,g=!1}}var E=!1,T=null,P=-1,j=5,D=-1;function re(){return!(e.unstable_now()-D<j)}function le(){if(T!==null){var L=e.unstable_now();D=L;var R=!0;try{R=T(!0,L)}finally{R?ge():(E=!1,T=null)}}else E=!1}var ge;if(typeof p=="function")ge=function(){p(le)};else if(typeof MessageChannel<"u"){var ie=new MessageChannel,xt=ie.port2;ie.port1.onmessage=le,ge=function(){xt.postMessage(null)}}else ge=function(){S(le,0)};function Z(L){T=L,E||(E=!0,ge())}function Fe(L,R){P=S(function(){L(e.unstable_now())},R)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(L){L.callback=null},e.unstable_continueExecution=function(){y||g||(y=!0,Z(k))},e.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<L?Math.floor(1e3/L):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(L){switch(d){case 1:case 2:case 3:var R=3;break;default:R=d}var N=d;d=R;try{return L()}finally{d=N}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(L,R){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var N=d;d=L;try{return R()}finally{d=N}},e.unstable_scheduleCallback=function(L,R,N){var _=e.unstable_now();switch(typeof N=="object"&&N!==null?(N=N.delay,N=typeof N=="number"&&0<N?_+N:_):N=_,L){case 1:var W=-1;break;case 2:W=250;break;case 5:W=**********;break;case 4:W=1e4;break;default:W=5e3}return W=N+W,L={id:c++,callback:R,priorityLevel:L,startTime:N,expirationTime:W,sortIndex:-1},N>_?(L.sortIndex=N,t(u,L),n(a)===null&&L===n(u)&&(v?(m(P),P=-1):v=!0,Fe(x,N-_))):(L.sortIndex=W,t(a,L),y||g||(y=!0,Z(k))),L},e.unstable_shouldYield=re,e.unstable_wrapCallback=function(L){var R=d;return function(){var N=d;d=R;try{return L.apply(this,arguments)}finally{d=N}}}})(Sf);wf.exports=Sf;var cm=wf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fm=A,Re=cm;function C(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var kf=new Set,Lr={};function pn(e,t){Un(e,t),Un(e+"Capture",t)}function Un(e,t){for(Lr[e]=t,e=0;e<t.length;e++)kf.add(t[e])}var pt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ms=Object.prototype.hasOwnProperty,dm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,qa={},ba={};function pm(e){return Ms.call(ba,e)?!0:Ms.call(qa,e)?!1:dm.test(e)?ba[e]=!0:(qa[e]=!0,!1)}function hm(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function mm(e,t,n,r){if(t===null||typeof t>"u"||hm(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Se(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ce[e]=new Se(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ce[t]=new Se(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ce[e]=new Se(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ce[e]=new Se(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ce[e]=new Se(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ce[e]=new Se(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ce[e]=new Se(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ce[e]=new Se(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ce[e]=new Se(e,5,!1,e.toLowerCase(),null,!1,!1)});var Il=/[\-:]([a-z])/g;function Bl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Il,Bl);ce[t]=new Se(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Il,Bl);ce[t]=new Se(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Il,Bl);ce[t]=new Se(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ce[e]=new Se(e,1,!1,e.toLowerCase(),null,!1,!1)});ce.xlinkHref=new Se("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ce[e]=new Se(e,1,!1,e.toLowerCase(),null,!0,!0)});function zl(e,t,n,r){var i=ce.hasOwnProperty(t)?ce[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(mm(t,n,i,r)&&(n=null),r||i===null?pm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var vt=fm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,oi=Symbol.for("react.element"),xn=Symbol.for("react.portal"),wn=Symbol.for("react.fragment"),Ul=Symbol.for("react.strict_mode"),As=Symbol.for("react.profiler"),Cf=Symbol.for("react.provider"),Pf=Symbol.for("react.context"),$l=Symbol.for("react.forward_ref"),Vs=Symbol.for("react.suspense"),js=Symbol.for("react.suspense_list"),Wl=Symbol.for("react.memo"),kt=Symbol.for("react.lazy"),Tf=Symbol.for("react.offscreen"),eu=Symbol.iterator;function bn(e){return e===null||typeof e!="object"?null:(e=eu&&e[eu]||e["@@iterator"],typeof e=="function"?e:null)}var X=Object.assign,Ho;function ur(e){if(Ho===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ho=t&&t[1]||""}return`
`+Ho+e}var Go=!1;function Ko(e,t){if(!e||Go)return"";Go=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,l=o.length-1;1<=s&&0<=l&&i[s]!==o[l];)l--;for(;1<=s&&0<=l;s--,l--)if(i[s]!==o[l]){if(s!==1||l!==1)do if(s--,l--,0>l||i[s]!==o[l]){var a=`
`+i[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{Go=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?ur(e):""}function gm(e){switch(e.tag){case 5:return ur(e.type);case 16:return ur("Lazy");case 13:return ur("Suspense");case 19:return ur("SuspenseList");case 0:case 2:case 15:return e=Ko(e.type,!1),e;case 11:return e=Ko(e.type.render,!1),e;case 1:return e=Ko(e.type,!0),e;default:return""}}function Rs(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case wn:return"Fragment";case xn:return"Portal";case As:return"Profiler";case Ul:return"StrictMode";case Vs:return"Suspense";case js:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Pf:return(e.displayName||"Context")+".Consumer";case Cf:return(e._context.displayName||"Context")+".Provider";case $l:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Wl:return t=e.displayName||null,t!==null?t:Rs(e.type)||"Memo";case kt:t=e._payload,e=e._init;try{return Rs(e(t))}catch{}}return null}function ym(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Rs(t);case 8:return t===Ul?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function It(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ef(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function vm(e){var t=Ef(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function si(e){e._valueTracker||(e._valueTracker=vm(e))}function Lf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Ef(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Bi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ds(e,t){var n=t.checked;return X({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function tu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=It(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Mf(e,t){t=t.checked,t!=null&&zl(e,"checked",t,!1)}function _s(e,t){Mf(e,t);var n=It(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ns(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ns(e,t.type,It(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function nu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ns(e,t,n){(t!=="number"||Bi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var cr=Array.isArray;function Nn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+It(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Fs(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(C(91));return X({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ru(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(C(92));if(cr(n)){if(1<n.length)throw Error(C(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:It(n)}}function Af(e,t){var n=It(t.value),r=It(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function iu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Vf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Os(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Vf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var li,jf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(li=li||document.createElement("div"),li.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=li.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Mr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var hr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},xm=["Webkit","ms","Moz","O"];Object.keys(hr).forEach(function(e){xm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),hr[t]=hr[e]})});function Rf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||hr.hasOwnProperty(e)&&hr[e]?(""+t).trim():t+"px"}function Df(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Rf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var wm=X({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Is(e,t){if(t){if(wm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(C(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(C(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(C(61))}if(t.style!=null&&typeof t.style!="object")throw Error(C(62))}}function Bs(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var zs=null;function Hl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Us=null,Fn=null,On=null;function ou(e){if(e=Jr(e)){if(typeof Us!="function")throw Error(C(280));var t=e.stateNode;t&&(t=Po(t),Us(e.stateNode,e.type,t))}}function _f(e){Fn?On?On.push(e):On=[e]:Fn=e}function Nf(){if(Fn){var e=Fn,t=On;if(On=Fn=null,ou(e),t)for(e=0;e<t.length;e++)ou(t[e])}}function Ff(e,t){return e(t)}function Of(){}var Qo=!1;function If(e,t,n){if(Qo)return e(t,n);Qo=!0;try{return Ff(e,t,n)}finally{Qo=!1,(Fn!==null||On!==null)&&(Of(),Nf())}}function Ar(e,t){var n=e.stateNode;if(n===null)return null;var r=Po(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(C(231,t,typeof n));return n}var $s=!1;if(pt)try{var er={};Object.defineProperty(er,"passive",{get:function(){$s=!0}}),window.addEventListener("test",er,er),window.removeEventListener("test",er,er)}catch{$s=!1}function Sm(e,t,n,r,i,o,s,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var mr=!1,zi=null,Ui=!1,Ws=null,km={onError:function(e){mr=!0,zi=e}};function Cm(e,t,n,r,i,o,s,l,a){mr=!1,zi=null,Sm.apply(km,arguments)}function Pm(e,t,n,r,i,o,s,l,a){if(Cm.apply(this,arguments),mr){if(mr){var u=zi;mr=!1,zi=null}else throw Error(C(198));Ui||(Ui=!0,Ws=u)}}function hn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Bf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function su(e){if(hn(e)!==e)throw Error(C(188))}function Tm(e){var t=e.alternate;if(!t){if(t=hn(e),t===null)throw Error(C(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return su(i),e;if(o===r)return su(i),t;o=o.sibling}throw Error(C(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s)throw Error(C(189))}}if(n.alternate!==r)throw Error(C(190))}if(n.tag!==3)throw Error(C(188));return n.stateNode.current===n?e:t}function zf(e){return e=Tm(e),e!==null?Uf(e):null}function Uf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Uf(e);if(t!==null)return t;e=e.sibling}return null}var $f=Re.unstable_scheduleCallback,lu=Re.unstable_cancelCallback,Em=Re.unstable_shouldYield,Lm=Re.unstable_requestPaint,J=Re.unstable_now,Mm=Re.unstable_getCurrentPriorityLevel,Gl=Re.unstable_ImmediatePriority,Wf=Re.unstable_UserBlockingPriority,$i=Re.unstable_NormalPriority,Am=Re.unstable_LowPriority,Hf=Re.unstable_IdlePriority,wo=null,nt=null;function Vm(e){if(nt&&typeof nt.onCommitFiberRoot=="function")try{nt.onCommitFiberRoot(wo,e,void 0,(e.current.flags&128)===128)}catch{}}var Ye=Math.clz32?Math.clz32:Dm,jm=Math.log,Rm=Math.LN2;function Dm(e){return e>>>=0,e===0?32:31-(jm(e)/Rm|0)|0}var ai=64,ui=4194304;function fr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Wi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~i;l!==0?r=fr(l):(o&=s,o!==0&&(r=fr(o)))}else s=n&~i,s!==0?r=fr(s):o!==0&&(r=fr(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ye(t),i=1<<n,r|=e[n],t&=~i;return r}function _m(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Nm(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-Ye(o),l=1<<s,a=i[s];a===-1?(!(l&n)||l&r)&&(i[s]=_m(l,t)):a<=t&&(e.expiredLanes|=l),o&=~l}}function Hs(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Gf(){var e=ai;return ai<<=1,!(ai&4194240)&&(ai=64),e}function Xo(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Yr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ye(t),e[t]=n}function Fm(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Ye(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function Kl(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ye(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var I=0;function Kf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Qf,Ql,Xf,Yf,Zf,Gs=!1,ci=[],At=null,Vt=null,jt=null,Vr=new Map,jr=new Map,Tt=[],Om="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function au(e,t){switch(e){case"focusin":case"focusout":At=null;break;case"dragenter":case"dragleave":Vt=null;break;case"mouseover":case"mouseout":jt=null;break;case"pointerover":case"pointerout":Vr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":jr.delete(t.pointerId)}}function tr(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=Jr(t),t!==null&&Ql(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Im(e,t,n,r,i){switch(t){case"focusin":return At=tr(At,e,t,n,r,i),!0;case"dragenter":return Vt=tr(Vt,e,t,n,r,i),!0;case"mouseover":return jt=tr(jt,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Vr.set(o,tr(Vr.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,jr.set(o,tr(jr.get(o)||null,e,t,n,r,i)),!0}return!1}function Jf(e){var t=en(e.target);if(t!==null){var n=hn(t);if(n!==null){if(t=n.tag,t===13){if(t=Bf(n),t!==null){e.blockedOn=t,Zf(e.priority,function(){Xf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Li(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ks(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);zs=r,n.target.dispatchEvent(r),zs=null}else return t=Jr(n),t!==null&&Ql(t),e.blockedOn=n,!1;t.shift()}return!0}function uu(e,t,n){Li(e)&&n.delete(t)}function Bm(){Gs=!1,At!==null&&Li(At)&&(At=null),Vt!==null&&Li(Vt)&&(Vt=null),jt!==null&&Li(jt)&&(jt=null),Vr.forEach(uu),jr.forEach(uu)}function nr(e,t){e.blockedOn===t&&(e.blockedOn=null,Gs||(Gs=!0,Re.unstable_scheduleCallback(Re.unstable_NormalPriority,Bm)))}function Rr(e){function t(i){return nr(i,e)}if(0<ci.length){nr(ci[0],e);for(var n=1;n<ci.length;n++){var r=ci[n];r.blockedOn===e&&(r.blockedOn=null)}}for(At!==null&&nr(At,e),Vt!==null&&nr(Vt,e),jt!==null&&nr(jt,e),Vr.forEach(t),jr.forEach(t),n=0;n<Tt.length;n++)r=Tt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Tt.length&&(n=Tt[0],n.blockedOn===null);)Jf(n),n.blockedOn===null&&Tt.shift()}var In=vt.ReactCurrentBatchConfig,Hi=!0;function zm(e,t,n,r){var i=I,o=In.transition;In.transition=null;try{I=1,Xl(e,t,n,r)}finally{I=i,In.transition=o}}function Um(e,t,n,r){var i=I,o=In.transition;In.transition=null;try{I=4,Xl(e,t,n,r)}finally{I=i,In.transition=o}}function Xl(e,t,n,r){if(Hi){var i=Ks(e,t,n,r);if(i===null)is(e,t,r,Gi,n),au(e,r);else if(Im(i,e,t,n,r))r.stopPropagation();else if(au(e,r),t&4&&-1<Om.indexOf(e)){for(;i!==null;){var o=Jr(i);if(o!==null&&Qf(o),o=Ks(e,t,n,r),o===null&&is(e,t,r,Gi,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else is(e,t,r,null,n)}}var Gi=null;function Ks(e,t,n,r){if(Gi=null,e=Hl(r),e=en(e),e!==null)if(t=hn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Bf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Gi=e,null}function qf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Mm()){case Gl:return 1;case Wf:return 4;case $i:case Am:return 16;case Hf:return 536870912;default:return 16}default:return 16}}var Lt=null,Yl=null,Mi=null;function bf(){if(Mi)return Mi;var e,t=Yl,n=t.length,r,i="value"in Lt?Lt.value:Lt.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return Mi=i.slice(e,1<r?1-r:void 0)}function Ai(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function fi(){return!0}function cu(){return!1}function Ne(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(o):o[l]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?fi:cu,this.isPropagationStopped=cu,this}return X(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=fi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=fi)},persist:function(){},isPersistent:fi}),t}var Jn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zl=Ne(Jn),Zr=X({},Jn,{view:0,detail:0}),$m=Ne(Zr),Yo,Zo,rr,So=X({},Zr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Jl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==rr&&(rr&&e.type==="mousemove"?(Yo=e.screenX-rr.screenX,Zo=e.screenY-rr.screenY):Zo=Yo=0,rr=e),Yo)},movementY:function(e){return"movementY"in e?e.movementY:Zo}}),fu=Ne(So),Wm=X({},So,{dataTransfer:0}),Hm=Ne(Wm),Gm=X({},Zr,{relatedTarget:0}),Jo=Ne(Gm),Km=X({},Jn,{animationName:0,elapsedTime:0,pseudoElement:0}),Qm=Ne(Km),Xm=X({},Jn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Ym=Ne(Xm),Zm=X({},Jn,{data:0}),du=Ne(Zm),Jm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},qm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},bm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function eg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=bm[e])?!!t[e]:!1}function Jl(){return eg}var tg=X({},Zr,{key:function(e){if(e.key){var t=Jm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ai(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?qm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Jl,charCode:function(e){return e.type==="keypress"?Ai(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ai(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ng=Ne(tg),rg=X({},So,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),pu=Ne(rg),ig=X({},Zr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Jl}),og=Ne(ig),sg=X({},Jn,{propertyName:0,elapsedTime:0,pseudoElement:0}),lg=Ne(sg),ag=X({},So,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ug=Ne(ag),cg=[9,13,27,32],ql=pt&&"CompositionEvent"in window,gr=null;pt&&"documentMode"in document&&(gr=document.documentMode);var fg=pt&&"TextEvent"in window&&!gr,ed=pt&&(!ql||gr&&8<gr&&11>=gr),hu=" ",mu=!1;function td(e,t){switch(e){case"keyup":return cg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function nd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Sn=!1;function dg(e,t){switch(e){case"compositionend":return nd(t);case"keypress":return t.which!==32?null:(mu=!0,hu);case"textInput":return e=t.data,e===hu&&mu?null:e;default:return null}}function pg(e,t){if(Sn)return e==="compositionend"||!ql&&td(e,t)?(e=bf(),Mi=Yl=Lt=null,Sn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ed&&t.locale!=="ko"?null:t.data;default:return null}}var hg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function gu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!hg[e.type]:t==="textarea"}function rd(e,t,n,r){_f(r),t=Ki(t,"onChange"),0<t.length&&(n=new Zl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var yr=null,Dr=null;function mg(e){hd(e,0)}function ko(e){var t=Pn(e);if(Lf(t))return e}function gg(e,t){if(e==="change")return t}var id=!1;if(pt){var qo;if(pt){var bo="oninput"in document;if(!bo){var yu=document.createElement("div");yu.setAttribute("oninput","return;"),bo=typeof yu.oninput=="function"}qo=bo}else qo=!1;id=qo&&(!document.documentMode||9<document.documentMode)}function vu(){yr&&(yr.detachEvent("onpropertychange",od),Dr=yr=null)}function od(e){if(e.propertyName==="value"&&ko(Dr)){var t=[];rd(t,Dr,e,Hl(e)),If(mg,t)}}function yg(e,t,n){e==="focusin"?(vu(),yr=t,Dr=n,yr.attachEvent("onpropertychange",od)):e==="focusout"&&vu()}function vg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ko(Dr)}function xg(e,t){if(e==="click")return ko(t)}function wg(e,t){if(e==="input"||e==="change")return ko(t)}function Sg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Je=typeof Object.is=="function"?Object.is:Sg;function _r(e,t){if(Je(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Ms.call(t,i)||!Je(e[i],t[i]))return!1}return!0}function xu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function wu(e,t){var n=xu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=xu(n)}}function sd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?sd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function ld(){for(var e=window,t=Bi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Bi(e.document)}return t}function bl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function kg(e){var t=ld(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&sd(n.ownerDocument.documentElement,n)){if(r!==null&&bl(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=wu(n,o);var s=wu(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Cg=pt&&"documentMode"in document&&11>=document.documentMode,kn=null,Qs=null,vr=null,Xs=!1;function Su(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Xs||kn==null||kn!==Bi(r)||(r=kn,"selectionStart"in r&&bl(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),vr&&_r(vr,r)||(vr=r,r=Ki(Qs,"onSelect"),0<r.length&&(t=new Zl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=kn)))}function di(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Cn={animationend:di("Animation","AnimationEnd"),animationiteration:di("Animation","AnimationIteration"),animationstart:di("Animation","AnimationStart"),transitionend:di("Transition","TransitionEnd")},es={},ad={};pt&&(ad=document.createElement("div").style,"AnimationEvent"in window||(delete Cn.animationend.animation,delete Cn.animationiteration.animation,delete Cn.animationstart.animation),"TransitionEvent"in window||delete Cn.transitionend.transition);function Co(e){if(es[e])return es[e];if(!Cn[e])return e;var t=Cn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ad)return es[e]=t[n];return e}var ud=Co("animationend"),cd=Co("animationiteration"),fd=Co("animationstart"),dd=Co("transitionend"),pd=new Map,ku="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function $t(e,t){pd.set(e,t),pn(t,[e])}for(var ts=0;ts<ku.length;ts++){var ns=ku[ts],Pg=ns.toLowerCase(),Tg=ns[0].toUpperCase()+ns.slice(1);$t(Pg,"on"+Tg)}$t(ud,"onAnimationEnd");$t(cd,"onAnimationIteration");$t(fd,"onAnimationStart");$t("dblclick","onDoubleClick");$t("focusin","onFocus");$t("focusout","onBlur");$t(dd,"onTransitionEnd");Un("onMouseEnter",["mouseout","mouseover"]);Un("onMouseLeave",["mouseout","mouseover"]);Un("onPointerEnter",["pointerout","pointerover"]);Un("onPointerLeave",["pointerout","pointerover"]);pn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));pn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));pn("onBeforeInput",["compositionend","keypress","textInput","paste"]);pn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));pn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));pn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var dr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Eg=new Set("cancel close invalid load scroll toggle".split(" ").concat(dr));function Cu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Pm(r,t,void 0,e),e.currentTarget=null}function hd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==o&&i.isPropagationStopped())break e;Cu(i,l,u),o=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,u=l.currentTarget,l=l.listener,a!==o&&i.isPropagationStopped())break e;Cu(i,l,u),o=a}}}if(Ui)throw e=Ws,Ui=!1,Ws=null,e}function z(e,t){var n=t[bs];n===void 0&&(n=t[bs]=new Set);var r=e+"__bubble";n.has(r)||(md(t,e,2,!1),n.add(r))}function rs(e,t,n){var r=0;t&&(r|=4),md(n,e,r,t)}var pi="_reactListening"+Math.random().toString(36).slice(2);function Nr(e){if(!e[pi]){e[pi]=!0,kf.forEach(function(n){n!=="selectionchange"&&(Eg.has(n)||rs(n,!1,e),rs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[pi]||(t[pi]=!0,rs("selectionchange",!1,t))}}function md(e,t,n,r){switch(qf(t)){case 1:var i=zm;break;case 4:i=Um;break;default:i=Xl}n=i.bind(null,t,n,e),i=void 0,!$s||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function is(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;s=s.return}for(;l!==null;){if(s=en(l),s===null)return;if(a=s.tag,a===5||a===6){r=o=s;continue e}l=l.parentNode}}r=r.return}If(function(){var u=o,c=Hl(n),f=[];e:{var d=pd.get(e);if(d!==void 0){var g=Zl,y=e;switch(e){case"keypress":if(Ai(n)===0)break e;case"keydown":case"keyup":g=ng;break;case"focusin":y="focus",g=Jo;break;case"focusout":y="blur",g=Jo;break;case"beforeblur":case"afterblur":g=Jo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=fu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Hm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=og;break;case ud:case cd:case fd:g=Qm;break;case dd:g=lg;break;case"scroll":g=$m;break;case"wheel":g=ug;break;case"copy":case"cut":case"paste":g=Ym;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=pu}var v=(t&4)!==0,S=!v&&e==="scroll",m=v?d!==null?d+"Capture":null:d;v=[];for(var p=u,h;p!==null;){h=p;var x=h.stateNode;if(h.tag===5&&x!==null&&(h=x,m!==null&&(x=Ar(p,m),x!=null&&v.push(Fr(p,x,h)))),S)break;p=p.return}0<v.length&&(d=new g(d,y,null,n,c),f.push({event:d,listeners:v}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",d&&n!==zs&&(y=n.relatedTarget||n.fromElement)&&(en(y)||y[ht]))break e;if((g||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,g?(y=n.relatedTarget||n.toElement,g=u,y=y?en(y):null,y!==null&&(S=hn(y),y!==S||y.tag!==5&&y.tag!==6)&&(y=null)):(g=null,y=u),g!==y)){if(v=fu,x="onMouseLeave",m="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(v=pu,x="onPointerLeave",m="onPointerEnter",p="pointer"),S=g==null?d:Pn(g),h=y==null?d:Pn(y),d=new v(x,p+"leave",g,n,c),d.target=S,d.relatedTarget=h,x=null,en(c)===u&&(v=new v(m,p+"enter",y,n,c),v.target=h,v.relatedTarget=S,x=v),S=x,g&&y)t:{for(v=g,m=y,p=0,h=v;h;h=vn(h))p++;for(h=0,x=m;x;x=vn(x))h++;for(;0<p-h;)v=vn(v),p--;for(;0<h-p;)m=vn(m),h--;for(;p--;){if(v===m||m!==null&&v===m.alternate)break t;v=vn(v),m=vn(m)}v=null}else v=null;g!==null&&Pu(f,d,g,v,!1),y!==null&&S!==null&&Pu(f,S,y,v,!0)}}e:{if(d=u?Pn(u):window,g=d.nodeName&&d.nodeName.toLowerCase(),g==="select"||g==="input"&&d.type==="file")var k=gg;else if(gu(d))if(id)k=wg;else{k=vg;var E=yg}else(g=d.nodeName)&&g.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(k=xg);if(k&&(k=k(e,u))){rd(f,k,n,c);break e}E&&E(e,d,u),e==="focusout"&&(E=d._wrapperState)&&E.controlled&&d.type==="number"&&Ns(d,"number",d.value)}switch(E=u?Pn(u):window,e){case"focusin":(gu(E)||E.contentEditable==="true")&&(kn=E,Qs=u,vr=null);break;case"focusout":vr=Qs=kn=null;break;case"mousedown":Xs=!0;break;case"contextmenu":case"mouseup":case"dragend":Xs=!1,Su(f,n,c);break;case"selectionchange":if(Cg)break;case"keydown":case"keyup":Su(f,n,c)}var T;if(ql)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else Sn?td(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(ed&&n.locale!=="ko"&&(Sn||P!=="onCompositionStart"?P==="onCompositionEnd"&&Sn&&(T=bf()):(Lt=c,Yl="value"in Lt?Lt.value:Lt.textContent,Sn=!0)),E=Ki(u,P),0<E.length&&(P=new du(P,e,null,n,c),f.push({event:P,listeners:E}),T?P.data=T:(T=nd(n),T!==null&&(P.data=T)))),(T=fg?dg(e,n):pg(e,n))&&(u=Ki(u,"onBeforeInput"),0<u.length&&(c=new du("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=T))}hd(f,t)})}function Fr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ki(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=Ar(e,n),o!=null&&r.unshift(Fr(e,o,i)),o=Ar(e,t),o!=null&&r.push(Fr(e,o,i))),e=e.return}return r}function vn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Pu(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,i?(a=Ar(n,o),a!=null&&s.unshift(Fr(n,a,l))):i||(a=Ar(n,o),a!=null&&s.push(Fr(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var Lg=/\r\n?/g,Mg=/\u0000|\uFFFD/g;function Tu(e){return(typeof e=="string"?e:""+e).replace(Lg,`
`).replace(Mg,"")}function hi(e,t,n){if(t=Tu(t),Tu(e)!==t&&n)throw Error(C(425))}function Qi(){}var Ys=null,Zs=null;function Js(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var qs=typeof setTimeout=="function"?setTimeout:void 0,Ag=typeof clearTimeout=="function"?clearTimeout:void 0,Eu=typeof Promise=="function"?Promise:void 0,Vg=typeof queueMicrotask=="function"?queueMicrotask:typeof Eu<"u"?function(e){return Eu.resolve(null).then(e).catch(jg)}:qs;function jg(e){setTimeout(function(){throw e})}function os(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Rr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Rr(t)}function Rt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Lu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var qn=Math.random().toString(36).slice(2),tt="__reactFiber$"+qn,Or="__reactProps$"+qn,ht="__reactContainer$"+qn,bs="__reactEvents$"+qn,Rg="__reactListeners$"+qn,Dg="__reactHandles$"+qn;function en(e){var t=e[tt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ht]||n[tt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Lu(e);e!==null;){if(n=e[tt])return n;e=Lu(e)}return t}e=n,n=e.parentNode}return null}function Jr(e){return e=e[tt]||e[ht],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Pn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(C(33))}function Po(e){return e[Or]||null}var el=[],Tn=-1;function Wt(e){return{current:e}}function U(e){0>Tn||(e.current=el[Tn],el[Tn]=null,Tn--)}function B(e,t){Tn++,el[Tn]=e.current,e.current=t}var Bt={},me=Wt(Bt),Pe=Wt(!1),an=Bt;function $n(e,t){var n=e.type.contextTypes;if(!n)return Bt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Te(e){return e=e.childContextTypes,e!=null}function Xi(){U(Pe),U(me)}function Mu(e,t,n){if(me.current!==Bt)throw Error(C(168));B(me,t),B(Pe,n)}function gd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(C(108,ym(e)||"Unknown",i));return X({},n,r)}function Yi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Bt,an=me.current,B(me,e),B(Pe,Pe.current),!0}function Au(e,t,n){var r=e.stateNode;if(!r)throw Error(C(169));n?(e=gd(e,t,an),r.__reactInternalMemoizedMergedChildContext=e,U(Pe),U(me),B(me,e)):U(Pe),B(Pe,n)}var st=null,To=!1,ss=!1;function yd(e){st===null?st=[e]:st.push(e)}function _g(e){To=!0,yd(e)}function Ht(){if(!ss&&st!==null){ss=!0;var e=0,t=I;try{var n=st;for(I=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}st=null,To=!1}catch(i){throw st!==null&&(st=st.slice(e+1)),$f(Gl,Ht),i}finally{I=t,ss=!1}}return null}var En=[],Ln=0,Zi=null,Ji=0,Be=[],ze=0,un=null,lt=1,at="";function Zt(e,t){En[Ln++]=Ji,En[Ln++]=Zi,Zi=e,Ji=t}function vd(e,t,n){Be[ze++]=lt,Be[ze++]=at,Be[ze++]=un,un=e;var r=lt;e=at;var i=32-Ye(r)-1;r&=~(1<<i),n+=1;var o=32-Ye(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,lt=1<<32-Ye(t)+i|n<<i|r,at=o+e}else lt=1<<o|n<<i|r,at=e}function ea(e){e.return!==null&&(Zt(e,1),vd(e,1,0))}function ta(e){for(;e===Zi;)Zi=En[--Ln],En[Ln]=null,Ji=En[--Ln],En[Ln]=null;for(;e===un;)un=Be[--ze],Be[ze]=null,at=Be[--ze],Be[ze]=null,lt=Be[--ze],Be[ze]=null}var je=null,Ve=null,H=!1,Xe=null;function xd(e,t){var n=Ue(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Vu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,je=e,Ve=Rt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,je=e,Ve=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=un!==null?{id:lt,overflow:at}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ue(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,je=e,Ve=null,!0):!1;default:return!1}}function tl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function nl(e){if(H){var t=Ve;if(t){var n=t;if(!Vu(e,t)){if(tl(e))throw Error(C(418));t=Rt(n.nextSibling);var r=je;t&&Vu(e,t)?xd(r,n):(e.flags=e.flags&-4097|2,H=!1,je=e)}}else{if(tl(e))throw Error(C(418));e.flags=e.flags&-4097|2,H=!1,je=e}}}function ju(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;je=e}function mi(e){if(e!==je)return!1;if(!H)return ju(e),H=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Js(e.type,e.memoizedProps)),t&&(t=Ve)){if(tl(e))throw wd(),Error(C(418));for(;t;)xd(e,t),t=Rt(t.nextSibling)}if(ju(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(C(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ve=Rt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ve=null}}else Ve=je?Rt(e.stateNode.nextSibling):null;return!0}function wd(){for(var e=Ve;e;)e=Rt(e.nextSibling)}function Wn(){Ve=je=null,H=!1}function na(e){Xe===null?Xe=[e]:Xe.push(e)}var Ng=vt.ReactCurrentBatchConfig;function ir(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(C(309));var r=n.stateNode}if(!r)throw Error(C(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var l=i.refs;s===null?delete l[o]:l[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(C(284));if(!n._owner)throw Error(C(290,e))}return e}function gi(e,t){throw e=Object.prototype.toString.call(t),Error(C(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ru(e){var t=e._init;return t(e._payload)}function Sd(e){function t(m,p){if(e){var h=m.deletions;h===null?(m.deletions=[p],m.flags|=16):h.push(p)}}function n(m,p){if(!e)return null;for(;p!==null;)t(m,p),p=p.sibling;return null}function r(m,p){for(m=new Map;p!==null;)p.key!==null?m.set(p.key,p):m.set(p.index,p),p=p.sibling;return m}function i(m,p){return m=Ft(m,p),m.index=0,m.sibling=null,m}function o(m,p,h){return m.index=h,e?(h=m.alternate,h!==null?(h=h.index,h<p?(m.flags|=2,p):h):(m.flags|=2,p)):(m.flags|=1048576,p)}function s(m){return e&&m.alternate===null&&(m.flags|=2),m}function l(m,p,h,x){return p===null||p.tag!==6?(p=ps(h,m.mode,x),p.return=m,p):(p=i(p,h),p.return=m,p)}function a(m,p,h,x){var k=h.type;return k===wn?c(m,p,h.props.children,x,h.key):p!==null&&(p.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===kt&&Ru(k)===p.type)?(x=i(p,h.props),x.ref=ir(m,p,h),x.return=m,x):(x=Fi(h.type,h.key,h.props,null,m.mode,x),x.ref=ir(m,p,h),x.return=m,x)}function u(m,p,h,x){return p===null||p.tag!==4||p.stateNode.containerInfo!==h.containerInfo||p.stateNode.implementation!==h.implementation?(p=hs(h,m.mode,x),p.return=m,p):(p=i(p,h.children||[]),p.return=m,p)}function c(m,p,h,x,k){return p===null||p.tag!==7?(p=sn(h,m.mode,x,k),p.return=m,p):(p=i(p,h),p.return=m,p)}function f(m,p,h){if(typeof p=="string"&&p!==""||typeof p=="number")return p=ps(""+p,m.mode,h),p.return=m,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case oi:return h=Fi(p.type,p.key,p.props,null,m.mode,h),h.ref=ir(m,null,p),h.return=m,h;case xn:return p=hs(p,m.mode,h),p.return=m,p;case kt:var x=p._init;return f(m,x(p._payload),h)}if(cr(p)||bn(p))return p=sn(p,m.mode,h,null),p.return=m,p;gi(m,p)}return null}function d(m,p,h,x){var k=p!==null?p.key:null;if(typeof h=="string"&&h!==""||typeof h=="number")return k!==null?null:l(m,p,""+h,x);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case oi:return h.key===k?a(m,p,h,x):null;case xn:return h.key===k?u(m,p,h,x):null;case kt:return k=h._init,d(m,p,k(h._payload),x)}if(cr(h)||bn(h))return k!==null?null:c(m,p,h,x,null);gi(m,h)}return null}function g(m,p,h,x,k){if(typeof x=="string"&&x!==""||typeof x=="number")return m=m.get(h)||null,l(p,m,""+x,k);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case oi:return m=m.get(x.key===null?h:x.key)||null,a(p,m,x,k);case xn:return m=m.get(x.key===null?h:x.key)||null,u(p,m,x,k);case kt:var E=x._init;return g(m,p,h,E(x._payload),k)}if(cr(x)||bn(x))return m=m.get(h)||null,c(p,m,x,k,null);gi(p,x)}return null}function y(m,p,h,x){for(var k=null,E=null,T=p,P=p=0,j=null;T!==null&&P<h.length;P++){T.index>P?(j=T,T=null):j=T.sibling;var D=d(m,T,h[P],x);if(D===null){T===null&&(T=j);break}e&&T&&D.alternate===null&&t(m,T),p=o(D,p,P),E===null?k=D:E.sibling=D,E=D,T=j}if(P===h.length)return n(m,T),H&&Zt(m,P),k;if(T===null){for(;P<h.length;P++)T=f(m,h[P],x),T!==null&&(p=o(T,p,P),E===null?k=T:E.sibling=T,E=T);return H&&Zt(m,P),k}for(T=r(m,T);P<h.length;P++)j=g(T,m,P,h[P],x),j!==null&&(e&&j.alternate!==null&&T.delete(j.key===null?P:j.key),p=o(j,p,P),E===null?k=j:E.sibling=j,E=j);return e&&T.forEach(function(re){return t(m,re)}),H&&Zt(m,P),k}function v(m,p,h,x){var k=bn(h);if(typeof k!="function")throw Error(C(150));if(h=k.call(h),h==null)throw Error(C(151));for(var E=k=null,T=p,P=p=0,j=null,D=h.next();T!==null&&!D.done;P++,D=h.next()){T.index>P?(j=T,T=null):j=T.sibling;var re=d(m,T,D.value,x);if(re===null){T===null&&(T=j);break}e&&T&&re.alternate===null&&t(m,T),p=o(re,p,P),E===null?k=re:E.sibling=re,E=re,T=j}if(D.done)return n(m,T),H&&Zt(m,P),k;if(T===null){for(;!D.done;P++,D=h.next())D=f(m,D.value,x),D!==null&&(p=o(D,p,P),E===null?k=D:E.sibling=D,E=D);return H&&Zt(m,P),k}for(T=r(m,T);!D.done;P++,D=h.next())D=g(T,m,P,D.value,x),D!==null&&(e&&D.alternate!==null&&T.delete(D.key===null?P:D.key),p=o(D,p,P),E===null?k=D:E.sibling=D,E=D);return e&&T.forEach(function(le){return t(m,le)}),H&&Zt(m,P),k}function S(m,p,h,x){if(typeof h=="object"&&h!==null&&h.type===wn&&h.key===null&&(h=h.props.children),typeof h=="object"&&h!==null){switch(h.$$typeof){case oi:e:{for(var k=h.key,E=p;E!==null;){if(E.key===k){if(k=h.type,k===wn){if(E.tag===7){n(m,E.sibling),p=i(E,h.props.children),p.return=m,m=p;break e}}else if(E.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===kt&&Ru(k)===E.type){n(m,E.sibling),p=i(E,h.props),p.ref=ir(m,E,h),p.return=m,m=p;break e}n(m,E);break}else t(m,E);E=E.sibling}h.type===wn?(p=sn(h.props.children,m.mode,x,h.key),p.return=m,m=p):(x=Fi(h.type,h.key,h.props,null,m.mode,x),x.ref=ir(m,p,h),x.return=m,m=x)}return s(m);case xn:e:{for(E=h.key;p!==null;){if(p.key===E)if(p.tag===4&&p.stateNode.containerInfo===h.containerInfo&&p.stateNode.implementation===h.implementation){n(m,p.sibling),p=i(p,h.children||[]),p.return=m,m=p;break e}else{n(m,p);break}else t(m,p);p=p.sibling}p=hs(h,m.mode,x),p.return=m,m=p}return s(m);case kt:return E=h._init,S(m,p,E(h._payload),x)}if(cr(h))return y(m,p,h,x);if(bn(h))return v(m,p,h,x);gi(m,h)}return typeof h=="string"&&h!==""||typeof h=="number"?(h=""+h,p!==null&&p.tag===6?(n(m,p.sibling),p=i(p,h),p.return=m,m=p):(n(m,p),p=ps(h,m.mode,x),p.return=m,m=p),s(m)):n(m,p)}return S}var Hn=Sd(!0),kd=Sd(!1),qi=Wt(null),bi=null,Mn=null,ra=null;function ia(){ra=Mn=bi=null}function oa(e){var t=qi.current;U(qi),e._currentValue=t}function rl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Bn(e,t){bi=e,ra=Mn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ce=!0),e.firstContext=null)}function We(e){var t=e._currentValue;if(ra!==e)if(e={context:e,memoizedValue:t,next:null},Mn===null){if(bi===null)throw Error(C(308));Mn=e,bi.dependencies={lanes:0,firstContext:e}}else Mn=Mn.next=e;return t}var tn=null;function sa(e){tn===null?tn=[e]:tn.push(e)}function Cd(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,sa(t)):(n.next=i.next,i.next=n),t.interleaved=n,mt(e,r)}function mt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Ct=!1;function la(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Pd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ct(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Dt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,O&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,mt(e,n)}return i=r.interleaved,i===null?(t.next=t,sa(r)):(t.next=i.next,i.next=t),r.interleaved=t,mt(e,n)}function Vi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Kl(e,n)}}function Du(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function eo(e,t,n,r){var i=e.updateQueue;Ct=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var a=l,u=a.next;a.next=null,s===null?o=u:s.next=u,s=a;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==s&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=a))}if(o!==null){var f=i.baseState;s=0,c=u=a=null,l=o;do{var d=l.lane,g=l.eventTime;if((r&d)===d){c!==null&&(c=c.next={eventTime:g,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var y=e,v=l;switch(d=t,g=n,v.tag){case 1:if(y=v.payload,typeof y=="function"){f=y.call(g,f,d);break e}f=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=v.payload,d=typeof y=="function"?y.call(g,f,d):y,d==null)break e;f=X({},f,d);break e;case 2:Ct=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,d=i.effects,d===null?i.effects=[l]:d.push(l))}else g={eventTime:g,lane:d,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=g,a=f):c=c.next=g,s|=d;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;d=l,l=d.next,d.next=null,i.lastBaseUpdate=d,i.shared.pending=null}}while(!0);if(c===null&&(a=f),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);fn|=s,e.lanes=s,e.memoizedState=f}}function _u(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(C(191,i));i.call(r)}}}var qr={},rt=Wt(qr),Ir=Wt(qr),Br=Wt(qr);function nn(e){if(e===qr)throw Error(C(174));return e}function aa(e,t){switch(B(Br,t),B(Ir,e),B(rt,qr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Os(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Os(t,e)}U(rt),B(rt,t)}function Gn(){U(rt),U(Ir),U(Br)}function Td(e){nn(Br.current);var t=nn(rt.current),n=Os(t,e.type);t!==n&&(B(Ir,e),B(rt,n))}function ua(e){Ir.current===e&&(U(rt),U(Ir))}var G=Wt(0);function to(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ls=[];function ca(){for(var e=0;e<ls.length;e++)ls[e]._workInProgressVersionPrimary=null;ls.length=0}var ji=vt.ReactCurrentDispatcher,as=vt.ReactCurrentBatchConfig,cn=0,Q=null,te=null,oe=null,no=!1,xr=!1,zr=0,Fg=0;function fe(){throw Error(C(321))}function fa(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Je(e[n],t[n]))return!1;return!0}function da(e,t,n,r,i,o){if(cn=o,Q=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ji.current=e===null||e.memoizedState===null?zg:Ug,e=n(r,i),xr){o=0;do{if(xr=!1,zr=0,25<=o)throw Error(C(301));o+=1,oe=te=null,t.updateQueue=null,ji.current=$g,e=n(r,i)}while(xr)}if(ji.current=ro,t=te!==null&&te.next!==null,cn=0,oe=te=Q=null,no=!1,t)throw Error(C(300));return e}function pa(){var e=zr!==0;return zr=0,e}function et(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return oe===null?Q.memoizedState=oe=e:oe=oe.next=e,oe}function He(){if(te===null){var e=Q.alternate;e=e!==null?e.memoizedState:null}else e=te.next;var t=oe===null?Q.memoizedState:oe.next;if(t!==null)oe=t,te=e;else{if(e===null)throw Error(C(310));te=e,e={memoizedState:te.memoizedState,baseState:te.baseState,baseQueue:te.baseQueue,queue:te.queue,next:null},oe===null?Q.memoizedState=oe=e:oe=oe.next=e}return oe}function Ur(e,t){return typeof t=="function"?t(e):t}function us(e){var t=He(),n=t.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=e;var r=te,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var l=s=null,a=null,u=o;do{var c=u.lane;if((cn&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=f,s=r):a=a.next=f,Q.lanes|=c,fn|=c}u=u.next}while(u!==null&&u!==o);a===null?s=r:a.next=l,Je(r,t.memoizedState)||(Ce=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,Q.lanes|=o,fn|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function cs(e){var t=He(),n=t.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);Je(o,t.memoizedState)||(Ce=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Ed(){}function Ld(e,t){var n=Q,r=He(),i=t(),o=!Je(r.memoizedState,i);if(o&&(r.memoizedState=i,Ce=!0),r=r.queue,ha(Vd.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||oe!==null&&oe.memoizedState.tag&1){if(n.flags|=2048,$r(9,Ad.bind(null,n,r,i,t),void 0,null),se===null)throw Error(C(349));cn&30||Md(n,t,i)}return i}function Md(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Ad(e,t,n,r){t.value=n,t.getSnapshot=r,jd(t)&&Rd(e)}function Vd(e,t,n){return n(function(){jd(t)&&Rd(e)})}function jd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Je(e,n)}catch{return!0}}function Rd(e){var t=mt(e,1);t!==null&&Ze(t,e,1,-1)}function Nu(e){var t=et();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ur,lastRenderedState:e},t.queue=e,e=e.dispatch=Bg.bind(null,Q,e),[t.memoizedState,e]}function $r(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Dd(){return He().memoizedState}function Ri(e,t,n,r){var i=et();Q.flags|=e,i.memoizedState=$r(1|t,n,void 0,r===void 0?null:r)}function Eo(e,t,n,r){var i=He();r=r===void 0?null:r;var o=void 0;if(te!==null){var s=te.memoizedState;if(o=s.destroy,r!==null&&fa(r,s.deps)){i.memoizedState=$r(t,n,o,r);return}}Q.flags|=e,i.memoizedState=$r(1|t,n,o,r)}function Fu(e,t){return Ri(8390656,8,e,t)}function ha(e,t){return Eo(2048,8,e,t)}function _d(e,t){return Eo(4,2,e,t)}function Nd(e,t){return Eo(4,4,e,t)}function Fd(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Od(e,t,n){return n=n!=null?n.concat([e]):null,Eo(4,4,Fd.bind(null,t,e),n)}function ma(){}function Id(e,t){var n=He();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&fa(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Bd(e,t){var n=He();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&fa(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function zd(e,t,n){return cn&21?(Je(n,t)||(n=Gf(),Q.lanes|=n,fn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ce=!0),e.memoizedState=n)}function Og(e,t){var n=I;I=n!==0&&4>n?n:4,e(!0);var r=as.transition;as.transition={};try{e(!1),t()}finally{I=n,as.transition=r}}function Ud(){return He().memoizedState}function Ig(e,t,n){var r=Nt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},$d(e))Wd(t,n);else if(n=Cd(e,t,n,r),n!==null){var i=xe();Ze(n,e,r,i),Hd(n,t,r)}}function Bg(e,t,n){var r=Nt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if($d(e))Wd(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,l=o(s,n);if(i.hasEagerState=!0,i.eagerState=l,Je(l,s)){var a=t.interleaved;a===null?(i.next=i,sa(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=Cd(e,t,i,r),n!==null&&(i=xe(),Ze(n,e,r,i),Hd(n,t,r))}}function $d(e){var t=e.alternate;return e===Q||t!==null&&t===Q}function Wd(e,t){xr=no=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Hd(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Kl(e,n)}}var ro={readContext:We,useCallback:fe,useContext:fe,useEffect:fe,useImperativeHandle:fe,useInsertionEffect:fe,useLayoutEffect:fe,useMemo:fe,useReducer:fe,useRef:fe,useState:fe,useDebugValue:fe,useDeferredValue:fe,useTransition:fe,useMutableSource:fe,useSyncExternalStore:fe,useId:fe,unstable_isNewReconciler:!1},zg={readContext:We,useCallback:function(e,t){return et().memoizedState=[e,t===void 0?null:t],e},useContext:We,useEffect:Fu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Ri(4194308,4,Fd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ri(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ri(4,2,e,t)},useMemo:function(e,t){var n=et();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=et();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Ig.bind(null,Q,e),[r.memoizedState,e]},useRef:function(e){var t=et();return e={current:e},t.memoizedState=e},useState:Nu,useDebugValue:ma,useDeferredValue:function(e){return et().memoizedState=e},useTransition:function(){var e=Nu(!1),t=e[0];return e=Og.bind(null,e[1]),et().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Q,i=et();if(H){if(n===void 0)throw Error(C(407));n=n()}else{if(n=t(),se===null)throw Error(C(349));cn&30||Md(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,Fu(Vd.bind(null,r,o,e),[e]),r.flags|=2048,$r(9,Ad.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=et(),t=se.identifierPrefix;if(H){var n=at,r=lt;n=(r&~(1<<32-Ye(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=zr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Fg++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Ug={readContext:We,useCallback:Id,useContext:We,useEffect:ha,useImperativeHandle:Od,useInsertionEffect:_d,useLayoutEffect:Nd,useMemo:Bd,useReducer:us,useRef:Dd,useState:function(){return us(Ur)},useDebugValue:ma,useDeferredValue:function(e){var t=He();return zd(t,te.memoizedState,e)},useTransition:function(){var e=us(Ur)[0],t=He().memoizedState;return[e,t]},useMutableSource:Ed,useSyncExternalStore:Ld,useId:Ud,unstable_isNewReconciler:!1},$g={readContext:We,useCallback:Id,useContext:We,useEffect:ha,useImperativeHandle:Od,useInsertionEffect:_d,useLayoutEffect:Nd,useMemo:Bd,useReducer:cs,useRef:Dd,useState:function(){return cs(Ur)},useDebugValue:ma,useDeferredValue:function(e){var t=He();return te===null?t.memoizedState=e:zd(t,te.memoizedState,e)},useTransition:function(){var e=cs(Ur)[0],t=He().memoizedState;return[e,t]},useMutableSource:Ed,useSyncExternalStore:Ld,useId:Ud,unstable_isNewReconciler:!1};function Ke(e,t){if(e&&e.defaultProps){t=X({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function il(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:X({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Lo={isMounted:function(e){return(e=e._reactInternals)?hn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=xe(),i=Nt(e),o=ct(r,i);o.payload=t,n!=null&&(o.callback=n),t=Dt(e,o,i),t!==null&&(Ze(t,e,i,r),Vi(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=xe(),i=Nt(e),o=ct(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Dt(e,o,i),t!==null&&(Ze(t,e,i,r),Vi(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=xe(),r=Nt(e),i=ct(n,r);i.tag=2,t!=null&&(i.callback=t),t=Dt(e,i,r),t!==null&&(Ze(t,e,r,n),Vi(t,e,r))}};function Ou(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!_r(n,r)||!_r(i,o):!0}function Gd(e,t,n){var r=!1,i=Bt,o=t.contextType;return typeof o=="object"&&o!==null?o=We(o):(i=Te(t)?an:me.current,r=t.contextTypes,o=(r=r!=null)?$n(e,i):Bt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Lo,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function Iu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Lo.enqueueReplaceState(t,t.state,null)}function ol(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},la(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=We(o):(o=Te(t)?an:me.current,i.context=$n(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(il(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Lo.enqueueReplaceState(i,i.state,null),eo(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Kn(e,t){try{var n="",r=t;do n+=gm(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function fs(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function sl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Wg=typeof WeakMap=="function"?WeakMap:Map;function Kd(e,t,n){n=ct(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){oo||(oo=!0,gl=r),sl(e,t)},n}function Qd(e,t,n){n=ct(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){sl(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){sl(e,t),typeof r!="function"&&(_t===null?_t=new Set([this]):_t.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Bu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Wg;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=ry.bind(null,e,t,n),t.then(e,e))}function zu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Uu(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ct(-1,1),t.tag=2,Dt(n,t,1))),n.lanes|=1),e)}var Hg=vt.ReactCurrentOwner,Ce=!1;function ve(e,t,n,r){t.child=e===null?kd(t,null,n,r):Hn(t,e.child,n,r)}function $u(e,t,n,r,i){n=n.render;var o=t.ref;return Bn(t,i),r=da(e,t,n,r,o,i),n=pa(),e!==null&&!Ce?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,gt(e,t,i)):(H&&n&&ea(t),t.flags|=1,ve(e,t,r,i),t.child)}function Wu(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!Ca(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Xd(e,t,o,r,i)):(e=Fi(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:_r,n(s,r)&&e.ref===t.ref)return gt(e,t,i)}return t.flags|=1,e=Ft(o,r),e.ref=t.ref,e.return=t,t.child=e}function Xd(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(_r(o,r)&&e.ref===t.ref)if(Ce=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Ce=!0);else return t.lanes=e.lanes,gt(e,t,i)}return ll(e,t,n,r,i)}function Yd(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},B(Vn,Ae),Ae|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,B(Vn,Ae),Ae|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,B(Vn,Ae),Ae|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,B(Vn,Ae),Ae|=r;return ve(e,t,i,n),t.child}function Zd(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ll(e,t,n,r,i){var o=Te(n)?an:me.current;return o=$n(t,o),Bn(t,i),n=da(e,t,n,r,o,i),r=pa(),e!==null&&!Ce?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,gt(e,t,i)):(H&&r&&ea(t),t.flags|=1,ve(e,t,n,i),t.child)}function Hu(e,t,n,r,i){if(Te(n)){var o=!0;Yi(t)}else o=!1;if(Bn(t,i),t.stateNode===null)Di(e,t),Gd(t,n,r),ol(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=We(u):(u=Te(n)?an:me.current,u=$n(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==u)&&Iu(t,s,r,u),Ct=!1;var d=t.memoizedState;s.state=d,eo(t,r,s,i),a=t.memoizedState,l!==r||d!==a||Pe.current||Ct?(typeof c=="function"&&(il(t,n,c,r),a=t.memoizedState),(l=Ct||Ou(t,n,l,r,d,a,u))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=u,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Pd(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:Ke(t.type,l),s.props=u,f=t.pendingProps,d=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=We(a):(a=Te(n)?an:me.current,a=$n(t,a));var g=n.getDerivedStateFromProps;(c=typeof g=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==f||d!==a)&&Iu(t,s,r,a),Ct=!1,d=t.memoizedState,s.state=d,eo(t,r,s,i);var y=t.memoizedState;l!==f||d!==y||Pe.current||Ct?(typeof g=="function"&&(il(t,n,g,r),y=t.memoizedState),(u=Ct||Ou(t,n,u,r,d,y,a)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,y,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,y,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),s.props=r,s.state=y,s.context=a,r=u):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return al(e,t,n,r,o,i)}function al(e,t,n,r,i,o){Zd(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&Au(t,n,!1),gt(e,t,o);r=t.stateNode,Hg.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Hn(t,e.child,null,o),t.child=Hn(t,null,l,o)):ve(e,t,l,o),t.memoizedState=r.state,i&&Au(t,n,!0),t.child}function Jd(e){var t=e.stateNode;t.pendingContext?Mu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Mu(e,t.context,!1),aa(e,t.containerInfo)}function Gu(e,t,n,r,i){return Wn(),na(i),t.flags|=256,ve(e,t,n,r),t.child}var ul={dehydrated:null,treeContext:null,retryLane:0};function cl(e){return{baseLanes:e,cachePool:null,transitions:null}}function qd(e,t,n){var r=t.pendingProps,i=G.current,o=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),B(G,i&1),e===null)return nl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=Vo(s,r,0,null),e=sn(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=cl(n),t.memoizedState=ul,e):ga(t,s));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return Gg(e,t,s,r,l,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,l=i.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Ft(i,a),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?o=Ft(l,o):(o=sn(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?cl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=ul,r}return o=e.child,e=o.sibling,r=Ft(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ga(e,t){return t=Vo({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function yi(e,t,n,r){return r!==null&&na(r),Hn(t,e.child,null,n),e=ga(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Gg(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=fs(Error(C(422))),yi(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=Vo({mode:"visible",children:r.children},i,0,null),o=sn(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&Hn(t,e.child,null,s),t.child.memoizedState=cl(s),t.memoizedState=ul,o);if(!(t.mode&1))return yi(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,o=Error(C(419)),r=fs(o,r,void 0),yi(e,t,s,r)}if(l=(s&e.childLanes)!==0,Ce||l){if(r=se,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,mt(e,i),Ze(r,e,i,-1))}return ka(),r=fs(Error(C(421))),yi(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=iy.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,Ve=Rt(i.nextSibling),je=t,H=!0,Xe=null,e!==null&&(Be[ze++]=lt,Be[ze++]=at,Be[ze++]=un,lt=e.id,at=e.overflow,un=t),t=ga(t,r.children),t.flags|=4096,t)}function Ku(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),rl(e.return,t,n)}function ds(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function bd(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(ve(e,t,r.children,n),r=G.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ku(e,n,t);else if(e.tag===19)Ku(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(B(G,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&to(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),ds(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&to(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}ds(t,!0,n,null,o);break;case"together":ds(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Di(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function gt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),fn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(C(153));if(t.child!==null){for(e=t.child,n=Ft(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ft(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Kg(e,t,n){switch(t.tag){case 3:Jd(t),Wn();break;case 5:Td(t);break;case 1:Te(t.type)&&Yi(t);break;case 4:aa(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;B(qi,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(B(G,G.current&1),t.flags|=128,null):n&t.child.childLanes?qd(e,t,n):(B(G,G.current&1),e=gt(e,t,n),e!==null?e.sibling:null);B(G,G.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return bd(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),B(G,G.current),r)break;return null;case 22:case 23:return t.lanes=0,Yd(e,t,n)}return gt(e,t,n)}var ep,fl,tp,np;ep=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};fl=function(){};tp=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,nn(rt.current);var o=null;switch(n){case"input":i=Ds(e,i),r=Ds(e,r),o=[];break;case"select":i=X({},i,{value:void 0}),r=X({},r,{value:void 0}),o=[];break;case"textarea":i=Fs(e,i),r=Fs(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Qi)}Is(n,r);var s;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var l=i[u];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Lr.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var a=r[u];if(l=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(o||(o=[]),o.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(o=o||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(o=o||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Lr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&z("scroll",e),o||l===a||(o=[])):(o=o||[]).push(u,a))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};np=function(e,t,n,r){n!==r&&(t.flags|=4)};function or(e,t){if(!H)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function de(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Qg(e,t,n){var r=t.pendingProps;switch(ta(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return de(t),null;case 1:return Te(t.type)&&Xi(),de(t),null;case 3:return r=t.stateNode,Gn(),U(Pe),U(me),ca(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(mi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Xe!==null&&(xl(Xe),Xe=null))),fl(e,t),de(t),null;case 5:ua(t);var i=nn(Br.current);if(n=t.type,e!==null&&t.stateNode!=null)tp(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(C(166));return de(t),null}if(e=nn(rt.current),mi(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[tt]=t,r[Or]=o,e=(t.mode&1)!==0,n){case"dialog":z("cancel",r),z("close",r);break;case"iframe":case"object":case"embed":z("load",r);break;case"video":case"audio":for(i=0;i<dr.length;i++)z(dr[i],r);break;case"source":z("error",r);break;case"img":case"image":case"link":z("error",r),z("load",r);break;case"details":z("toggle",r);break;case"input":tu(r,o),z("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},z("invalid",r);break;case"textarea":ru(r,o),z("invalid",r)}Is(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var l=o[s];s==="children"?typeof l=="string"?r.textContent!==l&&(o.suppressHydrationWarning!==!0&&hi(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(o.suppressHydrationWarning!==!0&&hi(r.textContent,l,e),i=["children",""+l]):Lr.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&z("scroll",r)}switch(n){case"input":si(r),nu(r,o,!0);break;case"textarea":si(r),iu(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=Qi)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Vf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[tt]=t,e[Or]=r,ep(e,t,!1,!1),t.stateNode=e;e:{switch(s=Bs(n,r),n){case"dialog":z("cancel",e),z("close",e),i=r;break;case"iframe":case"object":case"embed":z("load",e),i=r;break;case"video":case"audio":for(i=0;i<dr.length;i++)z(dr[i],e);i=r;break;case"source":z("error",e),i=r;break;case"img":case"image":case"link":z("error",e),z("load",e),i=r;break;case"details":z("toggle",e),i=r;break;case"input":tu(e,r),i=Ds(e,r),z("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=X({},r,{value:void 0}),z("invalid",e);break;case"textarea":ru(e,r),i=Fs(e,r),z("invalid",e);break;default:i=r}Is(n,i),l=i;for(o in l)if(l.hasOwnProperty(o)){var a=l[o];o==="style"?Df(e,a):o==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&jf(e,a)):o==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Mr(e,a):typeof a=="number"&&Mr(e,""+a):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Lr.hasOwnProperty(o)?a!=null&&o==="onScroll"&&z("scroll",e):a!=null&&zl(e,o,a,s))}switch(n){case"input":si(e),nu(e,r,!1);break;case"textarea":si(e),iu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+It(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?Nn(e,!!r.multiple,o,!1):r.defaultValue!=null&&Nn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Qi)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return de(t),null;case 6:if(e&&t.stateNode!=null)np(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(C(166));if(n=nn(Br.current),nn(rt.current),mi(t)){if(r=t.stateNode,n=t.memoizedProps,r[tt]=t,(o=r.nodeValue!==n)&&(e=je,e!==null))switch(e.tag){case 3:hi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&hi(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[tt]=t,t.stateNode=r}return de(t),null;case 13:if(U(G),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(H&&Ve!==null&&t.mode&1&&!(t.flags&128))wd(),Wn(),t.flags|=98560,o=!1;else if(o=mi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(C(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(C(317));o[tt]=t}else Wn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;de(t),o=!1}else Xe!==null&&(xl(Xe),Xe=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||G.current&1?ne===0&&(ne=3):ka())),t.updateQueue!==null&&(t.flags|=4),de(t),null);case 4:return Gn(),fl(e,t),e===null&&Nr(t.stateNode.containerInfo),de(t),null;case 10:return oa(t.type._context),de(t),null;case 17:return Te(t.type)&&Xi(),de(t),null;case 19:if(U(G),o=t.memoizedState,o===null)return de(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)or(o,!1);else{if(ne!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=to(e),s!==null){for(t.flags|=128,or(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return B(G,G.current&1|2),t.child}e=e.sibling}o.tail!==null&&J()>Qn&&(t.flags|=128,r=!0,or(o,!1),t.lanes=4194304)}else{if(!r)if(e=to(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),or(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!H)return de(t),null}else 2*J()-o.renderingStartTime>Qn&&n!==1073741824&&(t.flags|=128,r=!0,or(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=J(),t.sibling=null,n=G.current,B(G,r?n&1|2:n&1),t):(de(t),null);case 22:case 23:return Sa(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ae&1073741824&&(de(t),t.subtreeFlags&6&&(t.flags|=8192)):de(t),null;case 24:return null;case 25:return null}throw Error(C(156,t.tag))}function Xg(e,t){switch(ta(t),t.tag){case 1:return Te(t.type)&&Xi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Gn(),U(Pe),U(me),ca(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ua(t),null;case 13:if(U(G),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(C(340));Wn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return U(G),null;case 4:return Gn(),null;case 10:return oa(t.type._context),null;case 22:case 23:return Sa(),null;case 24:return null;default:return null}}var vi=!1,he=!1,Yg=typeof WeakSet=="function"?WeakSet:Set,M=null;function An(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Y(e,t,r)}else n.current=null}function dl(e,t,n){try{n()}catch(r){Y(e,t,r)}}var Qu=!1;function Zg(e,t){if(Ys=Hi,e=ld(),bl(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,u=0,c=0,f=e,d=null;t:for(;;){for(var g;f!==n||i!==0&&f.nodeType!==3||(l=s+i),f!==o||r!==0&&f.nodeType!==3||(a=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(g=f.firstChild)!==null;)d=f,f=g;for(;;){if(f===e)break t;if(d===n&&++u===i&&(l=s),d===o&&++c===r&&(a=s),(g=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=g}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Zs={focusedElem:e,selectionRange:n},Hi=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var v=y.memoizedProps,S=y.memoizedState,m=t.stateNode,p=m.getSnapshotBeforeUpdate(t.elementType===t.type?v:Ke(t.type,v),S);m.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var h=t.stateNode.containerInfo;h.nodeType===1?h.textContent="":h.nodeType===9&&h.documentElement&&h.removeChild(h.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(C(163))}}catch(x){Y(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return y=Qu,Qu=!1,y}function wr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&dl(t,n,o)}i=i.next}while(i!==r)}}function Mo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function pl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function rp(e){var t=e.alternate;t!==null&&(e.alternate=null,rp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[tt],delete t[Or],delete t[bs],delete t[Rg],delete t[Dg])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ip(e){return e.tag===5||e.tag===3||e.tag===4}function Xu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ip(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function hl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Qi));else if(r!==4&&(e=e.child,e!==null))for(hl(e,t,n),e=e.sibling;e!==null;)hl(e,t,n),e=e.sibling}function ml(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ml(e,t,n),e=e.sibling;e!==null;)ml(e,t,n),e=e.sibling}var ae=null,Qe=!1;function wt(e,t,n){for(n=n.child;n!==null;)op(e,t,n),n=n.sibling}function op(e,t,n){if(nt&&typeof nt.onCommitFiberUnmount=="function")try{nt.onCommitFiberUnmount(wo,n)}catch{}switch(n.tag){case 5:he||An(n,t);case 6:var r=ae,i=Qe;ae=null,wt(e,t,n),ae=r,Qe=i,ae!==null&&(Qe?(e=ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ae.removeChild(n.stateNode));break;case 18:ae!==null&&(Qe?(e=ae,n=n.stateNode,e.nodeType===8?os(e.parentNode,n):e.nodeType===1&&os(e,n),Rr(e)):os(ae,n.stateNode));break;case 4:r=ae,i=Qe,ae=n.stateNode.containerInfo,Qe=!0,wt(e,t,n),ae=r,Qe=i;break;case 0:case 11:case 14:case 15:if(!he&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&dl(n,t,s),i=i.next}while(i!==r)}wt(e,t,n);break;case 1:if(!he&&(An(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Y(n,t,l)}wt(e,t,n);break;case 21:wt(e,t,n);break;case 22:n.mode&1?(he=(r=he)||n.memoizedState!==null,wt(e,t,n),he=r):wt(e,t,n);break;default:wt(e,t,n)}}function Yu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Yg),t.forEach(function(r){var i=oy.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Ge(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:ae=l.stateNode,Qe=!1;break e;case 3:ae=l.stateNode.containerInfo,Qe=!0;break e;case 4:ae=l.stateNode.containerInfo,Qe=!0;break e}l=l.return}if(ae===null)throw Error(C(160));op(o,s,i),ae=null,Qe=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){Y(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)sp(t,e),t=t.sibling}function sp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ge(t,e),be(e),r&4){try{wr(3,e,e.return),Mo(3,e)}catch(v){Y(e,e.return,v)}try{wr(5,e,e.return)}catch(v){Y(e,e.return,v)}}break;case 1:Ge(t,e),be(e),r&512&&n!==null&&An(n,n.return);break;case 5:if(Ge(t,e),be(e),r&512&&n!==null&&An(n,n.return),e.flags&32){var i=e.stateNode;try{Mr(i,"")}catch(v){Y(e,e.return,v)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&o.type==="radio"&&o.name!=null&&Mf(i,o),Bs(l,s);var u=Bs(l,o);for(s=0;s<a.length;s+=2){var c=a[s],f=a[s+1];c==="style"?Df(i,f):c==="dangerouslySetInnerHTML"?jf(i,f):c==="children"?Mr(i,f):zl(i,c,f,u)}switch(l){case"input":_s(i,o);break;case"textarea":Af(i,o);break;case"select":var d=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var g=o.value;g!=null?Nn(i,!!o.multiple,g,!1):d!==!!o.multiple&&(o.defaultValue!=null?Nn(i,!!o.multiple,o.defaultValue,!0):Nn(i,!!o.multiple,o.multiple?[]:"",!1))}i[Or]=o}catch(v){Y(e,e.return,v)}}break;case 6:if(Ge(t,e),be(e),r&4){if(e.stateNode===null)throw Error(C(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(v){Y(e,e.return,v)}}break;case 3:if(Ge(t,e),be(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Rr(t.containerInfo)}catch(v){Y(e,e.return,v)}break;case 4:Ge(t,e),be(e);break;case 13:Ge(t,e),be(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(xa=J())),r&4&&Yu(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(he=(u=he)||c,Ge(t,e),he=u):Ge(t,e),be(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(M=e,c=e.child;c!==null;){for(f=M=c;M!==null;){switch(d=M,g=d.child,d.tag){case 0:case 11:case 14:case 15:wr(4,d,d.return);break;case 1:An(d,d.return);var y=d.stateNode;if(typeof y.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(v){Y(r,n,v)}}break;case 5:An(d,d.return);break;case 22:if(d.memoizedState!==null){Ju(f);continue}}g!==null?(g.return=d,M=g):Ju(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{i=f.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(l=f.stateNode,a=f.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=Rf("display",s))}catch(v){Y(e,e.return,v)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(v){Y(e,e.return,v)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Ge(t,e),be(e),r&4&&Yu(e);break;case 21:break;default:Ge(t,e),be(e)}}function be(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(ip(n)){var r=n;break e}n=n.return}throw Error(C(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Mr(i,""),r.flags&=-33);var o=Xu(e);ml(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,l=Xu(e);hl(e,l,s);break;default:throw Error(C(161))}}catch(a){Y(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Jg(e,t,n){M=e,lp(e)}function lp(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var i=M,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||vi;if(!s){var l=i.alternate,a=l!==null&&l.memoizedState!==null||he;l=vi;var u=he;if(vi=s,(he=a)&&!u)for(M=i;M!==null;)s=M,a=s.child,s.tag===22&&s.memoizedState!==null?qu(i):a!==null?(a.return=s,M=a):qu(i);for(;o!==null;)M=o,lp(o),o=o.sibling;M=i,vi=l,he=u}Zu(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,M=o):Zu(e)}}function Zu(e){for(;M!==null;){var t=M;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:he||Mo(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!he)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Ke(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&_u(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}_u(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&Rr(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(C(163))}he||t.flags&512&&pl(t)}catch(d){Y(t,t.return,d)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function Ju(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function qu(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Mo(4,t)}catch(a){Y(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){Y(t,i,a)}}var o=t.return;try{pl(t)}catch(a){Y(t,o,a)}break;case 5:var s=t.return;try{pl(t)}catch(a){Y(t,s,a)}}}catch(a){Y(t,t.return,a)}if(t===e){M=null;break}var l=t.sibling;if(l!==null){l.return=t.return,M=l;break}M=t.return}}var qg=Math.ceil,io=vt.ReactCurrentDispatcher,ya=vt.ReactCurrentOwner,$e=vt.ReactCurrentBatchConfig,O=0,se=null,ee=null,ue=0,Ae=0,Vn=Wt(0),ne=0,Wr=null,fn=0,Ao=0,va=0,Sr=null,ke=null,xa=0,Qn=1/0,ot=null,oo=!1,gl=null,_t=null,xi=!1,Mt=null,so=0,kr=0,yl=null,_i=-1,Ni=0;function xe(){return O&6?J():_i!==-1?_i:_i=J()}function Nt(e){return e.mode&1?O&2&&ue!==0?ue&-ue:Ng.transition!==null?(Ni===0&&(Ni=Gf()),Ni):(e=I,e!==0||(e=window.event,e=e===void 0?16:qf(e.type)),e):1}function Ze(e,t,n,r){if(50<kr)throw kr=0,yl=null,Error(C(185));Yr(e,n,r),(!(O&2)||e!==se)&&(e===se&&(!(O&2)&&(Ao|=n),ne===4&&Et(e,ue)),Ee(e,r),n===1&&O===0&&!(t.mode&1)&&(Qn=J()+500,To&&Ht()))}function Ee(e,t){var n=e.callbackNode;Nm(e,t);var r=Wi(e,e===se?ue:0);if(r===0)n!==null&&lu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&lu(n),t===1)e.tag===0?_g(bu.bind(null,e)):yd(bu.bind(null,e)),Vg(function(){!(O&6)&&Ht()}),n=null;else{switch(Kf(r)){case 1:n=Gl;break;case 4:n=Wf;break;case 16:n=$i;break;case 536870912:n=Hf;break;default:n=$i}n=mp(n,ap.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ap(e,t){if(_i=-1,Ni=0,O&6)throw Error(C(327));var n=e.callbackNode;if(zn()&&e.callbackNode!==n)return null;var r=Wi(e,e===se?ue:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=lo(e,r);else{t=r;var i=O;O|=2;var o=cp();(se!==e||ue!==t)&&(ot=null,Qn=J()+500,on(e,t));do try{ty();break}catch(l){up(e,l)}while(!0);ia(),io.current=o,O=i,ee!==null?t=0:(se=null,ue=0,t=ne)}if(t!==0){if(t===2&&(i=Hs(e),i!==0&&(r=i,t=vl(e,i))),t===1)throw n=Wr,on(e,0),Et(e,r),Ee(e,J()),n;if(t===6)Et(e,r);else{if(i=e.current.alternate,!(r&30)&&!bg(i)&&(t=lo(e,r),t===2&&(o=Hs(e),o!==0&&(r=o,t=vl(e,o))),t===1))throw n=Wr,on(e,0),Et(e,r),Ee(e,J()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(C(345));case 2:Jt(e,ke,ot);break;case 3:if(Et(e,r),(r&130023424)===r&&(t=xa+500-J(),10<t)){if(Wi(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){xe(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=qs(Jt.bind(null,e,ke,ot),t);break}Jt(e,ke,ot);break;case 4:if(Et(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-Ye(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=J()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*qg(r/1960))-r,10<r){e.timeoutHandle=qs(Jt.bind(null,e,ke,ot),r);break}Jt(e,ke,ot);break;case 5:Jt(e,ke,ot);break;default:throw Error(C(329))}}}return Ee(e,J()),e.callbackNode===n?ap.bind(null,e):null}function vl(e,t){var n=Sr;return e.current.memoizedState.isDehydrated&&(on(e,t).flags|=256),e=lo(e,t),e!==2&&(t=ke,ke=n,t!==null&&xl(t)),e}function xl(e){ke===null?ke=e:ke.push.apply(ke,e)}function bg(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!Je(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Et(e,t){for(t&=~va,t&=~Ao,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ye(t),r=1<<n;e[n]=-1,t&=~r}}function bu(e){if(O&6)throw Error(C(327));zn();var t=Wi(e,0);if(!(t&1))return Ee(e,J()),null;var n=lo(e,t);if(e.tag!==0&&n===2){var r=Hs(e);r!==0&&(t=r,n=vl(e,r))}if(n===1)throw n=Wr,on(e,0),Et(e,t),Ee(e,J()),n;if(n===6)throw Error(C(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Jt(e,ke,ot),Ee(e,J()),null}function wa(e,t){var n=O;O|=1;try{return e(t)}finally{O=n,O===0&&(Qn=J()+500,To&&Ht())}}function dn(e){Mt!==null&&Mt.tag===0&&!(O&6)&&zn();var t=O;O|=1;var n=$e.transition,r=I;try{if($e.transition=null,I=1,e)return e()}finally{I=r,$e.transition=n,O=t,!(O&6)&&Ht()}}function Sa(){Ae=Vn.current,U(Vn)}function on(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Ag(n)),ee!==null)for(n=ee.return;n!==null;){var r=n;switch(ta(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Xi();break;case 3:Gn(),U(Pe),U(me),ca();break;case 5:ua(r);break;case 4:Gn();break;case 13:U(G);break;case 19:U(G);break;case 10:oa(r.type._context);break;case 22:case 23:Sa()}n=n.return}if(se=e,ee=e=Ft(e.current,null),ue=Ae=t,ne=0,Wr=null,va=Ao=fn=0,ke=Sr=null,tn!==null){for(t=0;t<tn.length;t++)if(n=tn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}tn=null}return e}function up(e,t){do{var n=ee;try{if(ia(),ji.current=ro,no){for(var r=Q.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}no=!1}if(cn=0,oe=te=Q=null,xr=!1,zr=0,ya.current=null,n===null||n.return===null){ne=1,Wr=t,ee=null;break}e:{var o=e,s=n.return,l=n,a=t;if(t=ue,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=l,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var g=zu(s);if(g!==null){g.flags&=-257,Uu(g,s,l,o,t),g.mode&1&&Bu(o,u,t),t=g,a=u;var y=t.updateQueue;if(y===null){var v=new Set;v.add(a),t.updateQueue=v}else y.add(a);break e}else{if(!(t&1)){Bu(o,u,t),ka();break e}a=Error(C(426))}}else if(H&&l.mode&1){var S=zu(s);if(S!==null){!(S.flags&65536)&&(S.flags|=256),Uu(S,s,l,o,t),na(Kn(a,l));break e}}o=a=Kn(a,l),ne!==4&&(ne=2),Sr===null?Sr=[o]:Sr.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=Kd(o,a,t);Du(o,m);break e;case 1:l=a;var p=o.type,h=o.stateNode;if(!(o.flags&128)&&(typeof p.getDerivedStateFromError=="function"||h!==null&&typeof h.componentDidCatch=="function"&&(_t===null||!_t.has(h)))){o.flags|=65536,t&=-t,o.lanes|=t;var x=Qd(o,l,t);Du(o,x);break e}}o=o.return}while(o!==null)}dp(n)}catch(k){t=k,ee===n&&n!==null&&(ee=n=n.return);continue}break}while(!0)}function cp(){var e=io.current;return io.current=ro,e===null?ro:e}function ka(){(ne===0||ne===3||ne===2)&&(ne=4),se===null||!(fn&268435455)&&!(Ao&268435455)||Et(se,ue)}function lo(e,t){var n=O;O|=2;var r=cp();(se!==e||ue!==t)&&(ot=null,on(e,t));do try{ey();break}catch(i){up(e,i)}while(!0);if(ia(),O=n,io.current=r,ee!==null)throw Error(C(261));return se=null,ue=0,ne}function ey(){for(;ee!==null;)fp(ee)}function ty(){for(;ee!==null&&!Em();)fp(ee)}function fp(e){var t=hp(e.alternate,e,Ae);e.memoizedProps=e.pendingProps,t===null?dp(e):ee=t,ya.current=null}function dp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Xg(n,t),n!==null){n.flags&=32767,ee=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ne=6,ee=null;return}}else if(n=Qg(n,t,Ae),n!==null){ee=n;return}if(t=t.sibling,t!==null){ee=t;return}ee=t=e}while(t!==null);ne===0&&(ne=5)}function Jt(e,t,n){var r=I,i=$e.transition;try{$e.transition=null,I=1,ny(e,t,n,r)}finally{$e.transition=i,I=r}return null}function ny(e,t,n,r){do zn();while(Mt!==null);if(O&6)throw Error(C(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(C(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Fm(e,o),e===se&&(ee=se=null,ue=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||xi||(xi=!0,mp($i,function(){return zn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=$e.transition,$e.transition=null;var s=I;I=1;var l=O;O|=4,ya.current=null,Zg(e,n),sp(n,e),kg(Zs),Hi=!!Ys,Zs=Ys=null,e.current=n,Jg(n),Lm(),O=l,I=s,$e.transition=o}else e.current=n;if(xi&&(xi=!1,Mt=e,so=i),o=e.pendingLanes,o===0&&(_t=null),Vm(n.stateNode),Ee(e,J()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(oo)throw oo=!1,e=gl,gl=null,e;return so&1&&e.tag!==0&&zn(),o=e.pendingLanes,o&1?e===yl?kr++:(kr=0,yl=e):kr=0,Ht(),null}function zn(){if(Mt!==null){var e=Kf(so),t=$e.transition,n=I;try{if($e.transition=null,I=16>e?16:e,Mt===null)var r=!1;else{if(e=Mt,Mt=null,so=0,O&6)throw Error(C(331));var i=O;for(O|=4,M=e.current;M!==null;){var o=M,s=o.child;if(M.flags&16){var l=o.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(M=u;M!==null;){var c=M;switch(c.tag){case 0:case 11:case 15:wr(8,c,o)}var f=c.child;if(f!==null)f.return=c,M=f;else for(;M!==null;){c=M;var d=c.sibling,g=c.return;if(rp(c),c===u){M=null;break}if(d!==null){d.return=g,M=d;break}M=g}}}var y=o.alternate;if(y!==null){var v=y.child;if(v!==null){y.child=null;do{var S=v.sibling;v.sibling=null,v=S}while(v!==null)}}M=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,M=s;else e:for(;M!==null;){if(o=M,o.flags&2048)switch(o.tag){case 0:case 11:case 15:wr(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,M=m;break e}M=o.return}}var p=e.current;for(M=p;M!==null;){s=M;var h=s.child;if(s.subtreeFlags&2064&&h!==null)h.return=s,M=h;else e:for(s=p;M!==null;){if(l=M,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Mo(9,l)}}catch(k){Y(l,l.return,k)}if(l===s){M=null;break e}var x=l.sibling;if(x!==null){x.return=l.return,M=x;break e}M=l.return}}if(O=i,Ht(),nt&&typeof nt.onPostCommitFiberRoot=="function")try{nt.onPostCommitFiberRoot(wo,e)}catch{}r=!0}return r}finally{I=n,$e.transition=t}}return!1}function ec(e,t,n){t=Kn(n,t),t=Kd(e,t,1),e=Dt(e,t,1),t=xe(),e!==null&&(Yr(e,1,t),Ee(e,t))}function Y(e,t,n){if(e.tag===3)ec(e,e,n);else for(;t!==null;){if(t.tag===3){ec(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(_t===null||!_t.has(r))){e=Kn(n,e),e=Qd(t,e,1),t=Dt(t,e,1),e=xe(),t!==null&&(Yr(t,1,e),Ee(t,e));break}}t=t.return}}function ry(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=xe(),e.pingedLanes|=e.suspendedLanes&n,se===e&&(ue&n)===n&&(ne===4||ne===3&&(ue&130023424)===ue&&500>J()-xa?on(e,0):va|=n),Ee(e,t)}function pp(e,t){t===0&&(e.mode&1?(t=ui,ui<<=1,!(ui&130023424)&&(ui=4194304)):t=1);var n=xe();e=mt(e,t),e!==null&&(Yr(e,t,n),Ee(e,n))}function iy(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),pp(e,n)}function oy(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(C(314))}r!==null&&r.delete(t),pp(e,n)}var hp;hp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Pe.current)Ce=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ce=!1,Kg(e,t,n);Ce=!!(e.flags&131072)}else Ce=!1,H&&t.flags&1048576&&vd(t,Ji,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Di(e,t),e=t.pendingProps;var i=$n(t,me.current);Bn(t,n),i=da(null,t,r,e,i,n);var o=pa();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Te(r)?(o=!0,Yi(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,la(t),i.updater=Lo,t.stateNode=i,i._reactInternals=t,ol(t,r,e,n),t=al(null,t,r,!0,o,n)):(t.tag=0,H&&o&&ea(t),ve(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Di(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=ly(r),e=Ke(r,e),i){case 0:t=ll(null,t,r,e,n);break e;case 1:t=Hu(null,t,r,e,n);break e;case 11:t=$u(null,t,r,e,n);break e;case 14:t=Wu(null,t,r,Ke(r.type,e),n);break e}throw Error(C(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),ll(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),Hu(e,t,r,i,n);case 3:e:{if(Jd(t),e===null)throw Error(C(387));r=t.pendingProps,o=t.memoizedState,i=o.element,Pd(e,t),eo(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=Kn(Error(C(423)),t),t=Gu(e,t,r,n,i);break e}else if(r!==i){i=Kn(Error(C(424)),t),t=Gu(e,t,r,n,i);break e}else for(Ve=Rt(t.stateNode.containerInfo.firstChild),je=t,H=!0,Xe=null,n=kd(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Wn(),r===i){t=gt(e,t,n);break e}ve(e,t,r,n)}t=t.child}return t;case 5:return Td(t),e===null&&nl(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,Js(r,i)?s=null:o!==null&&Js(r,o)&&(t.flags|=32),Zd(e,t),ve(e,t,s,n),t.child;case 6:return e===null&&nl(t),null;case 13:return qd(e,t,n);case 4:return aa(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Hn(t,null,r,n):ve(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),$u(e,t,r,i,n);case 7:return ve(e,t,t.pendingProps,n),t.child;case 8:return ve(e,t,t.pendingProps.children,n),t.child;case 12:return ve(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,B(qi,r._currentValue),r._currentValue=s,o!==null)if(Je(o.value,s)){if(o.children===i.children&&!Pe.current){t=gt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var l=o.dependencies;if(l!==null){s=o.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(o.tag===1){a=ct(-1,n&-n),a.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),rl(o.return,n,t),l.lanes|=n;break}a=a.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(C(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),rl(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}ve(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Bn(t,n),i=We(i),r=r(i),t.flags|=1,ve(e,t,r,n),t.child;case 14:return r=t.type,i=Ke(r,t.pendingProps),i=Ke(r.type,i),Wu(e,t,r,i,n);case 15:return Xd(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),Di(e,t),t.tag=1,Te(r)?(e=!0,Yi(t)):e=!1,Bn(t,n),Gd(t,r,i),ol(t,r,i,n),al(null,t,r,!0,e,n);case 19:return bd(e,t,n);case 22:return Yd(e,t,n)}throw Error(C(156,t.tag))};function mp(e,t){return $f(e,t)}function sy(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ue(e,t,n,r){return new sy(e,t,n,r)}function Ca(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ly(e){if(typeof e=="function")return Ca(e)?1:0;if(e!=null){if(e=e.$$typeof,e===$l)return 11;if(e===Wl)return 14}return 2}function Ft(e,t){var n=e.alternate;return n===null?(n=Ue(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Fi(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")Ca(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case wn:return sn(n.children,i,o,t);case Ul:s=8,i|=8;break;case As:return e=Ue(12,n,t,i|2),e.elementType=As,e.lanes=o,e;case Vs:return e=Ue(13,n,t,i),e.elementType=Vs,e.lanes=o,e;case js:return e=Ue(19,n,t,i),e.elementType=js,e.lanes=o,e;case Tf:return Vo(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Cf:s=10;break e;case Pf:s=9;break e;case $l:s=11;break e;case Wl:s=14;break e;case kt:s=16,r=null;break e}throw Error(C(130,e==null?e:typeof e,""))}return t=Ue(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function sn(e,t,n,r){return e=Ue(7,e,r,t),e.lanes=n,e}function Vo(e,t,n,r){return e=Ue(22,e,r,t),e.elementType=Tf,e.lanes=n,e.stateNode={isHidden:!1},e}function ps(e,t,n){return e=Ue(6,e,null,t),e.lanes=n,e}function hs(e,t,n){return t=Ue(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ay(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Xo(0),this.expirationTimes=Xo(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Xo(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Pa(e,t,n,r,i,o,s,l,a){return e=new ay(e,t,n,l,a),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Ue(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},la(o),e}function uy(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:xn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function gp(e){if(!e)return Bt;e=e._reactInternals;e:{if(hn(e)!==e||e.tag!==1)throw Error(C(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Te(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(C(171))}if(e.tag===1){var n=e.type;if(Te(n))return gd(e,n,t)}return t}function yp(e,t,n,r,i,o,s,l,a){return e=Pa(n,r,!0,e,i,o,s,l,a),e.context=gp(null),n=e.current,r=xe(),i=Nt(n),o=ct(r,i),o.callback=t??null,Dt(n,o,i),e.current.lanes=i,Yr(e,i,r),Ee(e,r),e}function jo(e,t,n,r){var i=t.current,o=xe(),s=Nt(i);return n=gp(n),t.context===null?t.context=n:t.pendingContext=n,t=ct(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Dt(i,t,s),e!==null&&(Ze(e,i,s,o),Vi(e,i,s)),s}function ao(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function tc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Ta(e,t){tc(e,t),(e=e.alternate)&&tc(e,t)}function cy(){return null}var vp=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ea(e){this._internalRoot=e}Ro.prototype.render=Ea.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(C(409));jo(e,t,null,null)};Ro.prototype.unmount=Ea.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;dn(function(){jo(null,e,null,null)}),t[ht]=null}};function Ro(e){this._internalRoot=e}Ro.prototype.unstable_scheduleHydration=function(e){if(e){var t=Yf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Tt.length&&t!==0&&t<Tt[n].priority;n++);Tt.splice(n,0,e),n===0&&Jf(e)}};function La(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Do(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function nc(){}function fy(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=ao(s);o.call(u)}}var s=yp(t,r,e,0,null,!1,!1,"",nc);return e._reactRootContainer=s,e[ht]=s.current,Nr(e.nodeType===8?e.parentNode:e),dn(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var u=ao(a);l.call(u)}}var a=Pa(e,0,!1,null,null,!1,!1,"",nc);return e._reactRootContainer=a,e[ht]=a.current,Nr(e.nodeType===8?e.parentNode:e),dn(function(){jo(t,a,n,r)}),a}function _o(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var l=i;i=function(){var a=ao(s);l.call(a)}}jo(t,s,e,i)}else s=fy(n,t,e,i,r);return ao(s)}Qf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=fr(t.pendingLanes);n!==0&&(Kl(t,n|1),Ee(t,J()),!(O&6)&&(Qn=J()+500,Ht()))}break;case 13:dn(function(){var r=mt(e,1);if(r!==null){var i=xe();Ze(r,e,1,i)}}),Ta(e,1)}};Ql=function(e){if(e.tag===13){var t=mt(e,134217728);if(t!==null){var n=xe();Ze(t,e,134217728,n)}Ta(e,134217728)}};Xf=function(e){if(e.tag===13){var t=Nt(e),n=mt(e,t);if(n!==null){var r=xe();Ze(n,e,t,r)}Ta(e,t)}};Yf=function(){return I};Zf=function(e,t){var n=I;try{return I=e,t()}finally{I=n}};Us=function(e,t,n){switch(t){case"input":if(_s(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Po(r);if(!i)throw Error(C(90));Lf(r),_s(r,i)}}}break;case"textarea":Af(e,n);break;case"select":t=n.value,t!=null&&Nn(e,!!n.multiple,t,!1)}};Ff=wa;Of=dn;var dy={usingClientEntryPoint:!1,Events:[Jr,Pn,Po,_f,Nf,wa]},sr={findFiberByHostInstance:en,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},py={bundleType:sr.bundleType,version:sr.version,rendererPackageName:sr.rendererPackageName,rendererConfig:sr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:vt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=zf(e),e===null?null:e.stateNode},findFiberByHostInstance:sr.findFiberByHostInstance||cy,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var wi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!wi.isDisabled&&wi.supportsFiber)try{wo=wi.inject(py),nt=wi}catch{}}_e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=dy;_e.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!La(t))throw Error(C(200));return uy(e,t,null,n)};_e.createRoot=function(e,t){if(!La(e))throw Error(C(299));var n=!1,r="",i=vp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=Pa(e,1,!1,null,null,n,!1,r,i),e[ht]=t.current,Nr(e.nodeType===8?e.parentNode:e),new Ea(t)};_e.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(C(188)):(e=Object.keys(e).join(","),Error(C(268,e)));return e=zf(t),e=e===null?null:e.stateNode,e};_e.flushSync=function(e){return dn(e)};_e.hydrate=function(e,t,n){if(!Do(t))throw Error(C(200));return _o(null,e,t,!0,n)};_e.hydrateRoot=function(e,t,n){if(!La(e))throw Error(C(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=vp;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=yp(t,null,e,1,n??null,i,!1,o,s),e[ht]=t.current,Nr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Ro(t)};_e.render=function(e,t,n){if(!Do(t))throw Error(C(200));return _o(null,e,t,!1,n)};_e.unmountComponentAtNode=function(e){if(!Do(e))throw Error(C(40));return e._reactRootContainer?(dn(function(){_o(null,null,e,!1,function(){e._reactRootContainer=null,e[ht]=null})}),!0):!1};_e.unstable_batchedUpdates=wa;_e.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Do(n))throw Error(C(200));if(e==null||e._reactInternals===void 0)throw Error(C(38));return _o(e,t,n,!1,r)};_e.version="18.3.1-next-f1338f8080-20240426";function xp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(xp)}catch(e){console.error(e)}}xp(),xf.exports=_e;var hy=xf.exports,rc=hy;Ls.createRoot=rc.createRoot,Ls.hydrateRoot=rc.hydrateRoot;const wp=A.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),No=A.createContext({}),Ma=A.createContext(null),Fo=typeof document<"u",my=Fo?A.useLayoutEffect:A.useEffect,Sp=A.createContext({strict:!1}),Aa=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),gy="framerAppearId",kp="data-"+Aa(gy);function yy(e,t,n,r){const{visualElement:i}=A.useContext(No),o=A.useContext(Sp),s=A.useContext(Ma),l=A.useContext(wp).reducedMotion,a=A.useRef();r=r||o.renderer,!a.current&&r&&(a.current=r(e,{visualState:t,parent:i,props:n,presenceContext:s,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:l}));const u=a.current;A.useInsertionEffect(()=>{u&&u.update(n,s)});const c=A.useRef(!!(n[kp]&&!window.HandoffComplete));return my(()=>{u&&(u.render(),c.current&&u.animationState&&u.animationState.animateChanges())}),A.useEffect(()=>{u&&(u.updateFeatures(),!c.current&&u.animationState&&u.animationState.animateChanges(),c.current&&(c.current=!1,window.HandoffComplete=!0))}),u}function jn(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function vy(e,t,n){return A.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):jn(n)&&(n.current=r))},[t])}function Hr(e){return typeof e=="string"||Array.isArray(e)}function Oo(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Va=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ja=["initial",...Va];function Io(e){return Oo(e.animate)||ja.some(t=>Hr(e[t]))}function Cp(e){return!!(Io(e)||e.variants)}function xy(e,t){if(Io(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Hr(n)?n:void 0,animate:Hr(r)?r:void 0}}return e.inherit!==!1?t:{}}function wy(e){const{initial:t,animate:n}=xy(e,A.useContext(No));return A.useMemo(()=>({initial:t,animate:n}),[ic(t),ic(n)])}function ic(e){return Array.isArray(e)?e.join(" "):e}const oc={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Gr={};for(const e in oc)Gr[e]={isEnabled:t=>oc[e].some(n=>!!t[n])};function Sy(e){for(const t in e)Gr[t]={...Gr[t],...e[t]}}const Pp=A.createContext({}),Tp=A.createContext({}),ky=Symbol.for("motionComponentSymbol");function Cy({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&Sy(e);function o(l,a){let u;const c={...A.useContext(wp),...l,layoutId:Py(l)},{isStatic:f}=c,d=wy(l),g=r(l,f);if(!f&&Fo){d.visualElement=yy(i,g,c,t);const y=A.useContext(Tp),v=A.useContext(Sp).strict;d.visualElement&&(u=d.visualElement.loadFeatures(c,v,e,y))}return A.createElement(No.Provider,{value:d},u&&d.visualElement?A.createElement(u,{visualElement:d.visualElement,...c}):null,n(i,l,vy(g,d.visualElement,a),g,f,d.visualElement))}const s=A.forwardRef(o);return s[ky]=i,s}function Py({layoutId:e}){const t=A.useContext(Pp).id;return t&&e!==void 0?t+"-"+e:e}function Ty(e){function t(r,i={}){return Cy(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const Ey=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Ra(e){return typeof e!="string"||e.includes("-")?!1:!!(Ey.indexOf(e)>-1||/[A-Z]/.test(e))}const uo={};function Ly(e){Object.assign(uo,e)}const br=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],mn=new Set(br);function Ep(e,{layout:t,layoutId:n}){return mn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!uo[e]||e==="opacity")}const Le=e=>!!(e&&e.getVelocity),My={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Ay=br.length;function Vy(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,i){let o="";for(let s=0;s<Ay;s++){const l=br[s];if(e[l]!==void 0){const a=My[l]||l;o+=`${a}(${e[l]}) `}}return t&&!e.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(e,r?"":o):n&&r&&(o="none"),o}const Lp=e=>t=>typeof t=="string"&&t.startsWith(e),Mp=Lp("--"),wl=Lp("var(--"),jy=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,Ry=(e,t)=>t&&typeof e=="number"?t.transform(e):e,zt=(e,t,n)=>Math.min(Math.max(n,e),t),gn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Cr={...gn,transform:e=>zt(0,1,e)},Si={...gn,default:1},Pr=e=>Math.round(e*1e5)/1e5,Bo=/(-)?([\d]*\.?[\d])+/g,Ap=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Dy=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function ei(e){return typeof e=="string"}const ti=e=>({test:t=>ei(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),St=ti("deg"),it=ti("%"),V=ti("px"),_y=ti("vh"),Ny=ti("vw"),sc={...it,parse:e=>it.parse(e)/100,transform:e=>it.transform(e*100)},lc={...gn,transform:Math.round},Vp={borderWidth:V,borderTopWidth:V,borderRightWidth:V,borderBottomWidth:V,borderLeftWidth:V,borderRadius:V,radius:V,borderTopLeftRadius:V,borderTopRightRadius:V,borderBottomRightRadius:V,borderBottomLeftRadius:V,width:V,maxWidth:V,height:V,maxHeight:V,size:V,top:V,right:V,bottom:V,left:V,padding:V,paddingTop:V,paddingRight:V,paddingBottom:V,paddingLeft:V,margin:V,marginTop:V,marginRight:V,marginBottom:V,marginLeft:V,rotate:St,rotateX:St,rotateY:St,rotateZ:St,scale:Si,scaleX:Si,scaleY:Si,scaleZ:Si,skew:St,skewX:St,skewY:St,distance:V,translateX:V,translateY:V,translateZ:V,x:V,y:V,z:V,perspective:V,transformPerspective:V,opacity:Cr,originX:sc,originY:sc,originZ:V,zIndex:lc,fillOpacity:Cr,strokeOpacity:Cr,numOctaves:lc};function Da(e,t,n,r){const{style:i,vars:o,transform:s,transformOrigin:l}=e;let a=!1,u=!1,c=!0;for(const f in t){const d=t[f];if(Mp(f)){o[f]=d;continue}const g=Vp[f],y=Ry(d,g);if(mn.has(f)){if(a=!0,s[f]=y,!c)continue;d!==(g.default||0)&&(c=!1)}else f.startsWith("origin")?(u=!0,l[f]=y):i[f]=y}if(t.transform||(a||r?i.transform=Vy(e.transform,n,c,r):i.transform&&(i.transform="none")),u){const{originX:f="50%",originY:d="50%",originZ:g=0}=l;i.transformOrigin=`${f} ${d} ${g}`}}const _a=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function jp(e,t,n){for(const r in t)!Le(t[r])&&!Ep(r,n)&&(e[r]=t[r])}function Fy({transformTemplate:e},t,n){return A.useMemo(()=>{const r=_a();return Da(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function Oy(e,t,n){const r=e.style||{},i={};return jp(i,r,e),Object.assign(i,Fy(e,t,n)),e.transformValues?e.transformValues(i):i}function Iy(e,t,n){const r={},i=Oy(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r}const By=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function co(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||By.has(e)}let Rp=e=>!co(e);function zy(e){e&&(Rp=t=>t.startsWith("on")?!co(t):e(t))}try{zy(require("@emotion/is-prop-valid").default)}catch{}function Uy(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(Rp(i)||n===!0&&co(i)||!t&&!co(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function ac(e,t,n){return typeof e=="string"?e:V.transform(t+n*e)}function $y(e,t,n){const r=ac(t,e.x,e.width),i=ac(n,e.y,e.height);return`${r} ${i}`}const Wy={offset:"stroke-dashoffset",array:"stroke-dasharray"},Hy={offset:"strokeDashoffset",array:"strokeDasharray"};function Gy(e,t,n=1,r=0,i=!0){e.pathLength=1;const o=i?Wy:Hy;e[o.offset]=V.transform(-r);const s=V.transform(t),l=V.transform(n);e[o.array]=`${s} ${l}`}function Na(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:o,pathLength:s,pathSpacing:l=1,pathOffset:a=0,...u},c,f,d){if(Da(e,u,c,d),f){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:g,style:y,dimensions:v}=e;g.transform&&(v&&(y.transform=g.transform),delete g.transform),v&&(i!==void 0||o!==void 0||y.transform)&&(y.transformOrigin=$y(v,i!==void 0?i:.5,o!==void 0?o:.5)),t!==void 0&&(g.x=t),n!==void 0&&(g.y=n),r!==void 0&&(g.scale=r),s!==void 0&&Gy(g,s,l,a,!1)}const Dp=()=>({..._a(),attrs:{}}),Fa=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Ky(e,t,n,r){const i=A.useMemo(()=>{const o=Dp();return Na(o,t,{enableHardwareAcceleration:!1},Fa(r),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};jp(o,e.style,e),i.style={...o,...i.style}}return i}function Qy(e=!1){return(n,r,i,{latestValues:o},s)=>{const a=(Ra(n)?Ky:Iy)(r,o,s,n),c={...Uy(r,typeof n=="string",e),...a,ref:i},{children:f}=r,d=A.useMemo(()=>Le(f)?f.get():f,[f]);return A.createElement(n,{...c,children:d})}}function _p(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const o in n)e.style.setProperty(o,n[o])}const Np=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Fp(e,t,n,r){_p(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(Np.has(i)?i:Aa(i),t.attrs[i])}function Oa(e,t){const{style:n}=e,r={};for(const i in n)(Le(n[i])||t.style&&Le(t.style[i])||Ep(i,e))&&(r[i]=n[i]);return r}function Op(e,t){const n=Oa(e,t);for(const r in e)if(Le(e[r])||Le(t[r])){const i=br.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[i]=e[r]}return n}function Ia(e,t,n,r={},i={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),t}function Xy(e){const t=A.useRef(null);return t.current===null&&(t.current=e()),t.current}const fo=e=>Array.isArray(e),Yy=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Zy=e=>fo(e)?e[e.length-1]||0:e;function Oi(e){const t=Le(e)?e.get():e;return Yy(t)?t.toValue():t}function Jy({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,o){const s={latestValues:qy(r,i,o,e),renderState:t()};return n&&(s.mount=l=>n(r,l,s)),s}const Ip=e=>(t,n)=>{const r=A.useContext(No),i=A.useContext(Ma),o=()=>Jy(e,t,r,i);return n?o():Xy(o)};function qy(e,t,n,r){const i={},o=r(e,{});for(const d in o)i[d]=Oi(o[d]);let{initial:s,animate:l}=e;const a=Io(e),u=Cp(e);t&&u&&!a&&e.inherit!==!1&&(s===void 0&&(s=t.initial),l===void 0&&(l=t.animate));let c=n?n.initial===!1:!1;c=c||s===!1;const f=c?l:s;return f&&typeof f!="boolean"&&!Oo(f)&&(Array.isArray(f)?f:[f]).forEach(g=>{const y=Ia(e,g);if(!y)return;const{transitionEnd:v,transition:S,...m}=y;for(const p in m){let h=m[p];if(Array.isArray(h)){const x=c?h.length-1:0;h=h[x]}h!==null&&(i[p]=h)}for(const p in v)i[p]=v[p]}),i}const q=e=>e;class uc{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function by(e){let t=new uc,n=new uc,r=0,i=!1,o=!1;const s=new WeakSet,l={schedule:(a,u=!1,c=!1)=>{const f=c&&i,d=f?t:n;return u&&s.add(a),d.add(a)&&f&&i&&(r=t.order.length),a},cancel:a=>{n.remove(a),s.delete(a)},process:a=>{if(i){o=!0;return}if(i=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let u=0;u<r;u++){const c=t.order[u];c(a),s.has(c)&&(l.schedule(c),e())}i=!1,o&&(o=!1,l.process(a))}};return l}const ki=["prepare","read","update","preRender","render","postRender"],ev=40;function tv(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=ki.reduce((f,d)=>(f[d]=by(()=>n=!0),f),{}),s=f=>o[f].process(i),l=()=>{const f=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(f-i.timestamp,ev),1),i.timestamp=f,i.isProcessing=!0,ki.forEach(s),i.isProcessing=!1,n&&t&&(r=!1,e(l))},a=()=>{n=!0,r=!0,i.isProcessing||e(l)};return{schedule:ki.reduce((f,d)=>{const g=o[d];return f[d]=(y,v=!1,S=!1)=>(n||a(),g.schedule(y,v,S)),f},{}),cancel:f=>ki.forEach(d=>o[d].cancel(f)),state:i,steps:o}}const{schedule:$,cancel:yt,state:pe,steps:ms}=tv(typeof requestAnimationFrame<"u"?requestAnimationFrame:q,!0),nv={useVisualState:Ip({scrapeMotionValuesFromProps:Op,createRenderState:Dp,onMount:(e,t,{renderState:n,latestValues:r})=>{$.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),$.render(()=>{Na(n,r,{enableHardwareAcceleration:!1},Fa(t.tagName),e.transformTemplate),Fp(t,n)})}})},rv={useVisualState:Ip({scrapeMotionValuesFromProps:Oa,createRenderState:_a})};function iv(e,{forwardMotionProps:t=!1},n,r){return{...Ra(e)?nv:rv,preloadedFeatures:n,useRender:Qy(t),createVisualElement:r,Component:e}}function ut(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const Bp=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function zo(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const ov=e=>t=>Bp(t)&&e(t,zo(t));function ft(e,t,n,r){return ut(e,t,ov(n),r)}const sv=(e,t)=>n=>t(e(n)),Ot=(...e)=>e.reduce(sv);function zp(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const cc=zp("dragHorizontal"),fc=zp("dragVertical");function Up(e){let t=!1;if(e==="y")t=fc();else if(e==="x")t=cc();else{const n=cc(),r=fc();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function $p(){const e=Up(!0);return e?(e(),!1):!0}class Gt{constructor(t){this.isMounted=!1,this.node=t}update(){}}function dc(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),i=(o,s)=>{if(o.pointerType==="touch"||$p())return;const l=e.getProps();e.animationState&&l.whileHover&&e.animationState.setActive("whileHover",t),l[r]&&$.update(()=>l[r](o,s))};return ft(e.current,n,i,{passive:!e.getProps()[r]})}class lv extends Gt{mount(){this.unmount=Ot(dc(this.node,!0),dc(this.node,!1))}unmount(){}}class av extends Gt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Ot(ut(this.node.current,"focus",()=>this.onFocus()),ut(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Wp=(e,t)=>t?e===t?!0:Wp(e,t.parentElement):!1;function gs(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,zo(n))}class uv extends Gt{constructor(){super(...arguments),this.removeStartListeners=q,this.removeEndListeners=q,this.removeAccessibleListeners=q,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),o=ft(window,"pointerup",(l,a)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:c,globalTapTarget:f}=this.node.getProps();$.update(()=>{!f&&!Wp(this.node.current,l.target)?c&&c(l,a):u&&u(l,a)})},{passive:!(r.onTap||r.onPointerUp)}),s=ft(window,"pointercancel",(l,a)=>this.cancelPress(l,a),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=Ot(o,s),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=o=>{if(o.key!=="Enter"||this.isPressing)return;const s=l=>{l.key!=="Enter"||!this.checkPressEnd()||gs("up",(a,u)=>{const{onTap:c}=this.node.getProps();c&&$.update(()=>c(a,u))})};this.removeEndListeners(),this.removeEndListeners=ut(this.node.current,"keyup",s),gs("down",(l,a)=>{this.startPress(l,a)})},n=ut(this.node.current,"keydown",t),r=()=>{this.isPressing&&gs("cancel",(o,s)=>this.cancelPress(o,s))},i=ut(this.node.current,"blur",r);this.removeAccessibleListeners=Ot(n,i)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&$.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!$p()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&$.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=ft(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=ut(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Ot(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const Sl=new WeakMap,ys=new WeakMap,cv=e=>{const t=Sl.get(e.target);t&&t(e)},fv=e=>{e.forEach(cv)};function dv({root:e,...t}){const n=e||document;ys.has(n)||ys.set(n,{});const r=ys.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(fv,{root:e,...t})),r[i]}function pv(e,t,n){const r=dv(t);return Sl.set(e,n),r.observe(e),()=>{Sl.delete(e),r.unobserve(e)}}const hv={some:0,all:1};class mv extends Gt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:o}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:hv[i]},l=a=>{const{isIntersecting:u}=a;if(this.isInView===u||(this.isInView=u,o&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:f}=this.node.getProps(),d=u?c:f;d&&d(a)};return pv(this.node.current,s,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(gv(t,n))&&this.startObserver()}unmount(){}}function gv({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const yv={inView:{Feature:mv},tap:{Feature:uv},focus:{Feature:av},hover:{Feature:lv}};function Hp(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function vv(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function xv(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function Uo(e,t,n){const r=e.getProps();return Ia(r,t,n!==void 0?n:r.custom,vv(e),xv(e))}let Ba=q;const ln=e=>e*1e3,dt=e=>e/1e3,wv={current:!1},Gp=e=>Array.isArray(e)&&typeof e[0]=="number";function Kp(e){return!!(!e||typeof e=="string"&&Qp[e]||Gp(e)||Array.isArray(e)&&e.every(Kp))}const pr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Qp={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:pr([0,.65,.55,1]),circOut:pr([.55,0,1,.45]),backIn:pr([.31,.01,.66,-.59]),backOut:pr([.33,1.53,.69,.99])};function Xp(e){if(e)return Gp(e)?pr(e):Array.isArray(e)?e.map(Xp):Qp[e]}function Sv(e,t,n,{delay:r=0,duration:i,repeat:o=0,repeatType:s="loop",ease:l,times:a}={}){const u={[t]:n};a&&(u.offset=a);const c=Xp(l);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:s==="reverse"?"alternate":"normal"})}function kv(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const Yp=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,Cv=1e-7,Pv=12;function Tv(e,t,n,r,i){let o,s,l=0;do s=t+(n-t)/2,o=Yp(s,r,i)-e,o>0?n=s:t=s;while(Math.abs(o)>Cv&&++l<Pv);return s}function ni(e,t,n,r){if(e===t&&n===r)return q;const i=o=>Tv(o,0,1,e,n);return o=>o===0||o===1?o:Yp(i(o),t,r)}const Ev=ni(.42,0,1,1),Lv=ni(0,0,.58,1),Zp=ni(.42,0,.58,1),Mv=e=>Array.isArray(e)&&typeof e[0]!="number",Jp=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,qp=e=>t=>1-e(1-t),za=e=>1-Math.sin(Math.acos(e)),bp=qp(za),Av=Jp(za),eh=ni(.33,1.53,.69,.99),Ua=qp(eh),Vv=Jp(Ua),jv=e=>(e*=2)<1?.5*Ua(e):.5*(2-Math.pow(2,-10*(e-1))),Rv={linear:q,easeIn:Ev,easeInOut:Zp,easeOut:Lv,circIn:za,circInOut:Av,circOut:bp,backIn:Ua,backInOut:Vv,backOut:eh,anticipate:jv},pc=e=>{if(Array.isArray(e)){Ba(e.length===4);const[t,n,r,i]=e;return ni(t,n,r,i)}else if(typeof e=="string")return Rv[e];return e},$a=(e,t)=>n=>!!(ei(n)&&Dy.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),th=(e,t,n)=>r=>{if(!ei(r))return r;const[i,o,s,l]=r.match(Bo);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:l!==void 0?parseFloat(l):1}},Dv=e=>zt(0,255,e),vs={...gn,transform:e=>Math.round(Dv(e))},rn={test:$a("rgb","red"),parse:th("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+vs.transform(e)+", "+vs.transform(t)+", "+vs.transform(n)+", "+Pr(Cr.transform(r))+")"};function _v(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const kl={test:$a("#"),parse:_v,transform:rn.transform},Rn={test:$a("hsl","hue"),parse:th("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+it.transform(Pr(t))+", "+it.transform(Pr(n))+", "+Pr(Cr.transform(r))+")"},ye={test:e=>rn.test(e)||kl.test(e)||Rn.test(e),parse:e=>rn.test(e)?rn.parse(e):Rn.test(e)?Rn.parse(e):kl.parse(e),transform:e=>ei(e)?e:e.hasOwnProperty("red")?rn.transform(e):Rn.transform(e)},K=(e,t,n)=>-n*e+n*t+e;function xs(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Nv({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,o=0,s=0;if(!t)i=o=s=n;else{const l=n<.5?n*(1+t):n+t-n*t,a=2*n-l;i=xs(a,l,e+1/3),o=xs(a,l,e),s=xs(a,l,e-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(s*255),alpha:r}}const ws=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},Fv=[kl,rn,Rn],Ov=e=>Fv.find(t=>t.test(e));function hc(e){const t=Ov(e);let n=t.parse(e);return t===Rn&&(n=Nv(n)),n}const nh=(e,t)=>{const n=hc(e),r=hc(t),i={...n};return o=>(i.red=ws(n.red,r.red,o),i.green=ws(n.green,r.green,o),i.blue=ws(n.blue,r.blue,o),i.alpha=K(n.alpha,r.alpha,o),rn.transform(i))};function Iv(e){var t,n;return isNaN(e)&&ei(e)&&(((t=e.match(Bo))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Ap))===null||n===void 0?void 0:n.length)||0)>0}const rh={regex:jy,countKey:"Vars",token:"${v}",parse:q},ih={regex:Ap,countKey:"Colors",token:"${c}",parse:ye.parse},oh={regex:Bo,countKey:"Numbers",token:"${n}",parse:gn.parse};function Ss(e,{regex:t,countKey:n,token:r,parse:i}){const o=e.tokenised.match(t);o&&(e["num"+n]=o.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...o.map(i)))}function po(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&Ss(n,rh),Ss(n,ih),Ss(n,oh),n}function sh(e){return po(e).values}function lh(e){const{values:t,numColors:n,numVars:r,tokenised:i}=po(e),o=t.length;return s=>{let l=i;for(let a=0;a<o;a++)a<r?l=l.replace(rh.token,s[a]):a<r+n?l=l.replace(ih.token,ye.transform(s[a])):l=l.replace(oh.token,Pr(s[a]));return l}}const Bv=e=>typeof e=="number"?0:e;function zv(e){const t=sh(e);return lh(e)(t.map(Bv))}const Ut={test:Iv,parse:sh,createTransformer:lh,getAnimatableNone:zv},ah=(e,t)=>n=>`${n>0?t:e}`;function uh(e,t){return typeof e=="number"?n=>K(e,t,n):ye.test(e)?nh(e,t):e.startsWith("var(")?ah(e,t):fh(e,t)}const ch=(e,t)=>{const n=[...e],r=n.length,i=e.map((o,s)=>uh(o,t[s]));return o=>{for(let s=0;s<r;s++)n[s]=i[s](o);return n}},Uv=(e,t)=>{const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=uh(e[i],t[i]));return i=>{for(const o in r)n[o]=r[o](i);return n}},fh=(e,t)=>{const n=Ut.createTransformer(t),r=po(e),i=po(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?Ot(ch(r.values,i.values),n):ah(e,t)},Kr=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},mc=(e,t)=>n=>K(e,t,n);function $v(e){return typeof e=="number"?mc:typeof e=="string"?ye.test(e)?nh:fh:Array.isArray(e)?ch:typeof e=="object"?Uv:mc}function Wv(e,t,n){const r=[],i=n||$v(e[0]),o=e.length-1;for(let s=0;s<o;s++){let l=i(e[s],e[s+1]);if(t){const a=Array.isArray(t)?t[s]||q:t;l=Ot(a,l)}r.push(l)}return r}function dh(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const o=e.length;if(Ba(o===t.length),o===1)return()=>t[0];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=Wv(t,r,i),l=s.length,a=u=>{let c=0;if(l>1)for(;c<e.length-2&&!(u<e[c+1]);c++);const f=Kr(e[c],e[c+1],u);return s[c](f)};return n?u=>a(zt(e[0],e[o-1],u)):a}function Hv(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=Kr(0,t,r);e.push(K(n,1,i))}}function Gv(e){const t=[0];return Hv(t,e.length-1),t}function Kv(e,t){return e.map(n=>n*t)}function Qv(e,t){return e.map(()=>t||Zp).splice(0,e.length-1)}function ho({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=Mv(r)?r.map(pc):pc(r),o={done:!1,value:t[0]},s=Kv(n&&n.length===t.length?n:Gv(t),e),l=dh(s,t,{ease:Array.isArray(i)?i:Qv(t,i)});return{calculatedDuration:e,next:a=>(o.value=l(a),o.done=a>=e,o)}}function ph(e,t){return t?e*(1e3/t):0}const Xv=5;function hh(e,t,n){const r=Math.max(t-Xv,0);return ph(n-e(r),t-r)}const ks=.001,Yv=.01,Zv=10,Jv=.05,qv=1;function bv({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,o,s=1-t;s=zt(Jv,qv,s),e=zt(Yv,Zv,dt(e)),s<1?(i=u=>{const c=u*s,f=c*e,d=c-n,g=Cl(u,s),y=Math.exp(-f);return ks-d/g*y},o=u=>{const f=u*s*e,d=f*n+n,g=Math.pow(s,2)*Math.pow(u,2)*e,y=Math.exp(-f),v=Cl(Math.pow(u,2),s);return(-i(u)+ks>0?-1:1)*((d-g)*y)/v}):(i=u=>{const c=Math.exp(-u*e),f=(u-n)*e+1;return-ks+c*f},o=u=>{const c=Math.exp(-u*e),f=(n-u)*(e*e);return c*f});const l=5/e,a=t0(i,o,l);if(e=ln(e),isNaN(a))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(a,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const e0=12;function t0(e,t,n){let r=n;for(let i=1;i<e0;i++)r=r-e(r)/t(r);return r}function Cl(e,t){return e*Math.sqrt(1-t*t)}const n0=["duration","bounce"],r0=["stiffness","damping","mass"];function gc(e,t){return t.some(n=>e[n]!==void 0)}function i0(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!gc(e,r0)&&gc(e,n0)){const n=bv(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function mh({keyframes:e,restDelta:t,restSpeed:n,...r}){const i=e[0],o=e[e.length-1],s={done:!1,value:i},{stiffness:l,damping:a,mass:u,duration:c,velocity:f,isResolvedFromDuration:d}=i0({...r,velocity:-dt(r.velocity||0)}),g=f||0,y=a/(2*Math.sqrt(l*u)),v=o-i,S=dt(Math.sqrt(l/u)),m=Math.abs(v)<5;n||(n=m?.01:2),t||(t=m?.005:.5);let p;if(y<1){const h=Cl(S,y);p=x=>{const k=Math.exp(-y*S*x);return o-k*((g+y*S*v)/h*Math.sin(h*x)+v*Math.cos(h*x))}}else if(y===1)p=h=>o-Math.exp(-S*h)*(v+(g+S*v)*h);else{const h=S*Math.sqrt(y*y-1);p=x=>{const k=Math.exp(-y*S*x),E=Math.min(h*x,300);return o-k*((g+y*S*v)*Math.sinh(E)+h*v*Math.cosh(E))/h}}return{calculatedDuration:d&&c||null,next:h=>{const x=p(h);if(d)s.done=h>=c;else{let k=g;h!==0&&(y<1?k=hh(p,h,x):k=0);const E=Math.abs(k)<=n,T=Math.abs(o-x)<=t;s.done=E&&T}return s.value=s.done?o:x,s}}}function yc({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:l,max:a,restDelta:u=.5,restSpeed:c}){const f=e[0],d={done:!1,value:f},g=P=>l!==void 0&&P<l||a!==void 0&&P>a,y=P=>l===void 0?a:a===void 0||Math.abs(l-P)<Math.abs(a-P)?l:a;let v=n*t;const S=f+v,m=s===void 0?S:s(S);m!==S&&(v=m-f);const p=P=>-v*Math.exp(-P/r),h=P=>m+p(P),x=P=>{const j=p(P),D=h(P);d.done=Math.abs(j)<=u,d.value=d.done?m:D};let k,E;const T=P=>{g(d.value)&&(k=P,E=mh({keyframes:[d.value,y(d.value)],velocity:hh(h,P,d.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return T(0),{calculatedDuration:null,next:P=>{let j=!1;return!E&&k===void 0&&(j=!0,x(P),T(P)),k!==void 0&&P>k?E.next(P-k):(!j&&x(P),d)}}}const o0=e=>{const t=({timestamp:n})=>e(n);return{start:()=>$.update(t,!0),stop:()=>yt(t),now:()=>pe.isProcessing?pe.timestamp:performance.now()}},vc=2e4;function xc(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<vc;)t+=n,r=e.next(t);return t>=vc?1/0:t}const s0={decay:yc,inertia:yc,tween:ho,keyframes:ho,spring:mh};function mo({autoplay:e=!0,delay:t=0,driver:n=o0,keyframes:r,type:i="keyframes",repeat:o=0,repeatDelay:s=0,repeatType:l="loop",onPlay:a,onStop:u,onComplete:c,onUpdate:f,...d}){let g=1,y=!1,v,S;const m=()=>{S=new Promise(_=>{v=_})};m();let p;const h=s0[i]||ho;let x;h!==ho&&typeof r[0]!="number"&&(x=dh([0,100],r,{clamp:!1}),r=[0,100]);const k=h({...d,keyframes:r});let E;l==="mirror"&&(E=h({...d,keyframes:[...r].reverse(),velocity:-(d.velocity||0)}));let T="idle",P=null,j=null,D=null;k.calculatedDuration===null&&o&&(k.calculatedDuration=xc(k));const{calculatedDuration:re}=k;let le=1/0,ge=1/0;re!==null&&(le=re+s,ge=le*(o+1)-s);let ie=0;const xt=_=>{if(j===null)return;g>0&&(j=Math.min(j,_)),g<0&&(j=Math.min(_-ge/g,j)),P!==null?ie=P:ie=Math.round(_-j)*g;const W=ie-t*(g>=0?1:-1),Kt=g>=0?W<0:W>ge;ie=Math.max(W,0),T==="finished"&&P===null&&(ie=ge);let qe=ie,yn=k;if(o){const $o=Math.min(ie,ge)/le;let ri=Math.floor($o),Xt=$o%1;!Xt&&$o>=1&&(Xt=1),Xt===1&&ri--,ri=Math.min(ri,o+1),!!(ri%2)&&(l==="reverse"?(Xt=1-Xt,s&&(Xt-=s/le)):l==="mirror"&&(yn=E)),qe=zt(0,1,Xt)*le}const Me=Kt?{done:!1,value:r[0]}:yn.next(qe);x&&(Me.value=x(Me.value));let{done:Qt}=Me;!Kt&&re!==null&&(Qt=g>=0?ie>=ge:ie<=0);const Uh=P===null&&(T==="finished"||T==="running"&&Qt);return f&&f(Me.value),Uh&&L(),Me},Z=()=>{p&&p.stop(),p=void 0},Fe=()=>{T="idle",Z(),v(),m(),j=D=null},L=()=>{T="finished",c&&c(),Z(),v()},R=()=>{if(y)return;p||(p=n(xt));const _=p.now();a&&a(),P!==null?j=_-P:(!j||T==="finished")&&(j=_),T==="finished"&&m(),D=j,P=null,T="running",p.start()};e&&R();const N={then(_,W){return S.then(_,W)},get time(){return dt(ie)},set time(_){_=ln(_),ie=_,P!==null||!p||g===0?P=_:j=p.now()-_/g},get duration(){const _=k.calculatedDuration===null?xc(k):k.calculatedDuration;return dt(_)},get speed(){return g},set speed(_){_===g||!p||(g=_,N.time=dt(ie))},get state(){return T},play:R,pause:()=>{T="paused",P=ie},stop:()=>{y=!0,T!=="idle"&&(T="idle",u&&u(),Fe())},cancel:()=>{D!==null&&xt(D),Fe()},complete:()=>{T="finished"},sample:_=>(j=0,xt(_))};return N}function l0(e){let t;return()=>(t===void 0&&(t=e()),t)}const a0=l0(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),u0=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),Ci=10,c0=2e4,f0=(e,t)=>t.type==="spring"||e==="backgroundColor"||!Kp(t.ease);function d0(e,t,{onUpdate:n,onComplete:r,...i}){if(!(a0()&&u0.has(t)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let s=!1,l,a,u=!1;const c=()=>{a=new Promise(h=>{l=h})};c();let{keyframes:f,duration:d=300,ease:g,times:y}=i;if(f0(t,i)){const h=mo({...i,repeat:0,delay:0});let x={done:!1,value:f[0]};const k=[];let E=0;for(;!x.done&&E<c0;)x=h.sample(E),k.push(x.value),E+=Ci;y=void 0,f=k,d=E-Ci,g="linear"}const v=Sv(e.owner.current,t,f,{...i,duration:d,ease:g,times:y}),S=()=>{u=!1,v.cancel()},m=()=>{u=!0,$.update(S),l(),c()};return v.onfinish=()=>{u||(e.set(kv(f,i)),r&&r(),m())},{then(h,x){return a.then(h,x)},attachTimeline(h){return v.timeline=h,v.onfinish=null,q},get time(){return dt(v.currentTime||0)},set time(h){v.currentTime=ln(h)},get speed(){return v.playbackRate},set speed(h){v.playbackRate=h},get duration(){return dt(d)},play:()=>{s||(v.play(),yt(S))},pause:()=>v.pause(),stop:()=>{if(s=!0,v.playState==="idle")return;const{currentTime:h}=v;if(h){const x=mo({...i,autoplay:!1});e.setWithVelocity(x.sample(h-Ci).value,x.sample(h).value,Ci)}m()},complete:()=>{u||v.finish()},cancel:m}}function p0({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:q,pause:q,stop:q,then:o=>(o(),Promise.resolve()),cancel:q,complete:q});return t?mo({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}const h0={type:"spring",stiffness:500,damping:25,restSpeed:10},m0=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),g0={type:"keyframes",duration:.8},y0={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},v0=(e,{keyframes:t})=>t.length>2?g0:mn.has(e)?e.startsWith("scale")?m0(t[1]):h0:y0,Pl=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Ut.test(t)||t==="0")&&!t.startsWith("url(")),x0=new Set(["brightness","contrast","saturate","opacity"]);function w0(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Bo)||[];if(!r)return e;const i=n.replace(r,"");let o=x0.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const S0=/([a-z-]*)\(.*?\)/g,Tl={...Ut,getAnimatableNone:e=>{const t=e.match(S0);return t?t.map(w0).join(" "):e}},k0={...Vp,color:ye,backgroundColor:ye,outlineColor:ye,fill:ye,stroke:ye,borderColor:ye,borderTopColor:ye,borderRightColor:ye,borderBottomColor:ye,borderLeftColor:ye,filter:Tl,WebkitFilter:Tl},Wa=e=>k0[e];function gh(e,t){let n=Wa(e);return n!==Tl&&(n=Ut),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const yh=e=>/^0[^.\s]+$/.test(e);function C0(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||yh(e)}function P0(e,t,n,r){const i=Pl(t,n);let o;Array.isArray(n)?o=[...n]:o=[null,n];const s=r.from!==void 0?r.from:e.get();let l;const a=[];for(let u=0;u<o.length;u++)o[u]===null&&(o[u]=u===0?s:o[u-1]),C0(o[u])&&a.push(u),typeof o[u]=="string"&&o[u]!=="none"&&o[u]!=="0"&&(l=o[u]);if(i&&a.length&&l)for(let u=0;u<a.length;u++){const c=a[u];o[c]=gh(t,l)}return o}function T0({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:l,from:a,elapsed:u,...c}){return!!Object.keys(c).length}function Ha(e,t){return e[t]||e.default||e}const E0={skipAnimations:!1},Ga=(e,t,n,r={})=>i=>{const o=Ha(r,e)||{},s=o.delay||r.delay||0;let{elapsed:l=0}=r;l=l-ln(s);const a=P0(t,e,n,o),u=a[0],c=a[a.length-1],f=Pl(e,u),d=Pl(e,c);let g={keyframes:a,velocity:t.getVelocity(),ease:"easeOut",...o,delay:-l,onUpdate:y=>{t.set(y),o.onUpdate&&o.onUpdate(y)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(T0(o)||(g={...g,...v0(e,g)}),g.duration&&(g.duration=ln(g.duration)),g.repeatDelay&&(g.repeatDelay=ln(g.repeatDelay)),!f||!d||wv.current||o.type===!1||E0.skipAnimations)return p0(g);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const y=d0(t,e,g);if(y)return y}return mo(g)};function go(e){return!!(Le(e)&&e.add)}const vh=e=>/^\-?\d*\.?\d+$/.test(e);function Ka(e,t){e.indexOf(t)===-1&&e.push(t)}function Qa(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Xa{constructor(){this.subscriptions=[]}add(t){return Ka(this.subscriptions,t),()=>Qa(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let o=0;o<i;o++){const s=this.subscriptions[o];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const L0=e=>!isNaN(parseFloat(e));class M0{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:o,timestamp:s}=pe;this.lastUpdated!==s&&(this.timeDelta=o,this.lastUpdated=s,$.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>$.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=L0(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new Xa);const r=this.events[t].add(n);return t==="change"?()=>{r(),$.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?ph(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Xn(e,t){return new M0(e,t)}const xh=e=>t=>t.test(e),A0={test:e=>e==="auto",parse:e=>e},wh=[gn,V,it,St,Ny,_y,A0],lr=e=>wh.find(xh(e)),V0=[...wh,ye,Ut],j0=e=>V0.find(xh(e));function R0(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Xn(n))}function D0(e,t){const n=Uo(e,t);let{transitionEnd:r={},transition:i={},...o}=n?e.makeTargetAnimatable(n,!1):{};o={...o,...r};for(const s in o){const l=Zy(o[s]);R0(e,s,l)}}function _0(e,t,n){var r,i;const o=Object.keys(t).filter(l=>!e.hasValue(l)),s=o.length;if(s)for(let l=0;l<s;l++){const a=o[l],u=t[a];let c=null;Array.isArray(u)&&(c=u[0]),c===null&&(c=(i=(r=n[a])!==null&&r!==void 0?r:e.readValue(a))!==null&&i!==void 0?i:t[a]),c!=null&&(typeof c=="string"&&(vh(c)||yh(c))?c=parseFloat(c):!j0(c)&&Ut.test(u)&&(c=gh(a,u)),e.addValue(a,Xn(c,{owner:e})),n[a]===void 0&&(n[a]=c),c!==null&&e.setBaseTarget(a,c))}}function N0(e,t){return t?(t[e]||t.default||t).from:void 0}function F0(e,t,n){const r={};for(const i in e){const o=N0(i,t);if(o!==void 0)r[i]=o;else{const s=n.getValue(i);s&&(r[i]=s.get())}}return r}function O0({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function I0(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function Sh(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...l}=e.makeTargetAnimatable(t);const a=e.getValue("willChange");r&&(o=r);const u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const f in l){const d=e.getValue(f),g=l[f];if(!d||g===void 0||c&&O0(c,f))continue;const y={delay:n,elapsed:0,...Ha(o||{},f)};if(window.HandoffAppearAnimations){const m=e.getProps()[kp];if(m){const p=window.HandoffAppearAnimations(m,f,d,$);p!==null&&(y.elapsed=p,y.isHandoff=!0)}}let v=!y.isHandoff&&!I0(d,g);if(y.type==="spring"&&(d.getVelocity()||y.velocity)&&(v=!1),d.animation&&(v=!1),v)continue;d.start(Ga(f,d,g,e.shouldReduceMotion&&mn.has(f)?{type:!1}:y));const S=d.animation;go(a)&&(a.add(f),S.then(()=>a.remove(f))),u.push(S)}return s&&Promise.all(u).then(()=>{s&&D0(e,s)}),u}function El(e,t,n={}){const r=Uo(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const o=r?()=>Promise.all(Sh(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(a=0)=>{const{delayChildren:u=0,staggerChildren:c,staggerDirection:f}=i;return B0(e,t,u+a,c,f,n)}:()=>Promise.resolve(),{when:l}=i;if(l){const[a,u]=l==="beforeChildren"?[o,s]:[s,o];return a().then(()=>u())}else return Promise.all([o(),s(n.delay)])}function B0(e,t,n=0,r=0,i=1,o){const s=[],l=(e.variantChildren.size-1)*r,a=i===1?(u=0)=>u*r:(u=0)=>l-u*r;return Array.from(e.variantChildren).sort(z0).forEach((u,c)=>{u.notify("AnimationStart",t),s.push(El(u,t,{...o,delay:n+a(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function z0(e,t){return e.sortNodePosition(t)}function U0(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(o=>El(e,o,n));r=Promise.all(i)}else if(typeof t=="string")r=El(e,t,n);else{const i=typeof t=="function"?Uo(e,t,n.custom):t;r=Promise.all(Sh(e,i,n))}return r.then(()=>e.notify("AnimationComplete",t))}const $0=[...Va].reverse(),W0=Va.length;function H0(e){return t=>Promise.all(t.map(({animation:n,options:r})=>U0(e,n,r)))}function G0(e){let t=H0(e);const n=Q0();let r=!0;const i=(a,u)=>{const c=Uo(e,u);if(c){const{transition:f,transitionEnd:d,...g}=c;a={...a,...g,...d}}return a};function o(a){t=a(e)}function s(a,u){const c=e.getProps(),f=e.getVariantContext(!0)||{},d=[],g=new Set;let y={},v=1/0;for(let m=0;m<W0;m++){const p=$0[m],h=n[p],x=c[p]!==void 0?c[p]:f[p],k=Hr(x),E=p===u?h.isActive:null;E===!1&&(v=m);let T=x===f[p]&&x!==c[p]&&k;if(T&&r&&e.manuallyAnimateOnMount&&(T=!1),h.protectedKeys={...y},!h.isActive&&E===null||!x&&!h.prevProp||Oo(x)||typeof x=="boolean")continue;let j=K0(h.prevProp,x)||p===u&&h.isActive&&!T&&k||m>v&&k,D=!1;const re=Array.isArray(x)?x:[x];let le=re.reduce(i,{});E===!1&&(le={});const{prevResolvedValues:ge={}}=h,ie={...ge,...le},xt=Z=>{j=!0,g.has(Z)&&(D=!0,g.delete(Z)),h.needsAnimating[Z]=!0};for(const Z in ie){const Fe=le[Z],L=ge[Z];if(y.hasOwnProperty(Z))continue;let R=!1;fo(Fe)&&fo(L)?R=!Hp(Fe,L):R=Fe!==L,R?Fe!==void 0?xt(Z):g.add(Z):Fe!==void 0&&g.has(Z)?xt(Z):h.protectedKeys[Z]=!0}h.prevProp=x,h.prevResolvedValues=le,h.isActive&&(y={...y,...le}),r&&e.blockInitialAnimation&&(j=!1),j&&(!T||D)&&d.push(...re.map(Z=>({animation:Z,options:{type:p,...a}})))}if(g.size){const m={};g.forEach(p=>{const h=e.getBaseTarget(p);h!==void 0&&(m[p]=h)}),d.push({animation:m})}let S=!!d.length;return r&&(c.initial===!1||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(S=!1),r=!1,S?t(d):Promise.resolve()}function l(a,u,c){var f;if(n[a].isActive===u)return Promise.resolve();(f=e.variantChildren)===null||f===void 0||f.forEach(g=>{var y;return(y=g.animationState)===null||y===void 0?void 0:y.setActive(a,u)}),n[a].isActive=u;const d=s(c,a);for(const g in n)n[g].protectedKeys={};return d}return{animateChanges:s,setActive:l,setAnimateFunction:o,getState:()=>n}}function K0(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Hp(t,e):!1}function Yt(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Q0(){return{animate:Yt(!0),whileInView:Yt(),whileHover:Yt(),whileTap:Yt(),whileDrag:Yt(),whileFocus:Yt(),exit:Yt()}}class X0 extends Gt{constructor(t){super(t),t.animationState||(t.animationState=G0(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),Oo(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let Y0=0;class Z0 extends Gt{constructor(){super(...arguments),this.id=Y0++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const o=this.node.animationState.setActive("exit",!t,{custom:r??this.node.getProps().custom});n&&!t&&o.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const J0={animation:{Feature:X0},exit:{Feature:Z0}},wc=(e,t)=>Math.abs(e-t);function q0(e,t){const n=wc(e.x,t.x),r=wc(e.y,t.y);return Math.sqrt(n**2+r**2)}class kh{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const f=Ps(this.lastMoveEventInfo,this.history),d=this.startEvent!==null,g=q0(f.offset,{x:0,y:0})>=3;if(!d&&!g)return;const{point:y}=f,{timestamp:v}=pe;this.history.push({...y,timestamp:v});const{onStart:S,onMove:m}=this.handlers;d||(S&&S(this.lastMoveEvent,f),this.startEvent=this.lastMoveEvent),m&&m(this.lastMoveEvent,f)},this.handlePointerMove=(f,d)=>{this.lastMoveEvent=f,this.lastMoveEventInfo=Cs(d,this.transformPagePoint),$.update(this.updatePoint,!0)},this.handlePointerUp=(f,d)=>{this.end();const{onEnd:g,onSessionEnd:y,resumeAnimation:v}=this.handlers;if(this.dragSnapToOrigin&&v&&v(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const S=Ps(f.type==="pointercancel"?this.lastMoveEventInfo:Cs(d,this.transformPagePoint),this.history);this.startEvent&&g&&g(f,S),y&&y(f,S)},!Bp(t))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const s=zo(t),l=Cs(s,this.transformPagePoint),{point:a}=l,{timestamp:u}=pe;this.history=[{...a,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,Ps(l,this.history)),this.removeListeners=Ot(ft(this.contextWindow,"pointermove",this.handlePointerMove),ft(this.contextWindow,"pointerup",this.handlePointerUp),ft(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),yt(this.updatePoint)}}function Cs(e,t){return t?{point:t(e.point)}:e}function Sc(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Ps({point:e},t){return{point:e,delta:Sc(e,Ch(t)),offset:Sc(e,b0(t)),velocity:e1(t,.1)}}function b0(e){return e[0]}function Ch(e){return e[e.length-1]}function e1(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=Ch(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>ln(t)));)n--;if(!r)return{x:0,y:0};const o=dt(i.timestamp-r.timestamp);if(o===0)return{x:0,y:0};const s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function De(e){return e.max-e.min}function Ll(e,t=0,n=.01){return Math.abs(e-t)<=n}function kc(e,t,n,r=.5){e.origin=r,e.originPoint=K(t.min,t.max,e.origin),e.scale=De(n)/De(t),(Ll(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=K(n.min,n.max,e.origin)-e.originPoint,(Ll(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Tr(e,t,n,r){kc(e.x,t.x,n.x,r?r.originX:void 0),kc(e.y,t.y,n.y,r?r.originY:void 0)}function Cc(e,t,n){e.min=n.min+t.min,e.max=e.min+De(t)}function t1(e,t,n){Cc(e.x,t.x,n.x),Cc(e.y,t.y,n.y)}function Pc(e,t,n){e.min=t.min-n.min,e.max=e.min+De(t)}function Er(e,t,n){Pc(e.x,t.x,n.x),Pc(e.y,t.y,n.y)}function n1(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?K(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?K(n,e,r.max):Math.min(e,n)),e}function Tc(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function r1(e,{top:t,left:n,bottom:r,right:i}){return{x:Tc(e.x,n,i),y:Tc(e.y,t,r)}}function Ec(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function i1(e,t){return{x:Ec(e.x,t.x),y:Ec(e.y,t.y)}}function o1(e,t){let n=.5;const r=De(e),i=De(t);return i>r?n=Kr(t.min,t.max-r,e.min):r>i&&(n=Kr(e.min,e.max-i,t.min)),zt(0,1,n)}function s1(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Ml=.35;function l1(e=Ml){return e===!1?e=0:e===!0&&(e=Ml),{x:Lc(e,"left","right"),y:Lc(e,"top","bottom")}}function Lc(e,t,n){return{min:Mc(e,t),max:Mc(e,n)}}function Mc(e,t){return typeof e=="number"?e:e[t]||0}const Ac=()=>({translate:0,scale:1,origin:0,originPoint:0}),Dn=()=>({x:Ac(),y:Ac()}),Vc=()=>({min:0,max:0}),b=()=>({x:Vc(),y:Vc()});function Ie(e){return[e("x"),e("y")]}function Ph({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function a1({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function u1(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Ts(e){return e===void 0||e===1}function Al({scale:e,scaleX:t,scaleY:n}){return!Ts(e)||!Ts(t)||!Ts(n)}function qt(e){return Al(e)||Th(e)||e.z||e.rotate||e.rotateX||e.rotateY}function Th(e){return jc(e.x)||jc(e.y)}function jc(e){return e&&e!=="0%"}function yo(e,t,n){const r=e-n,i=t*r;return n+i}function Rc(e,t,n,r,i){return i!==void 0&&(e=yo(e,i,r)),yo(e,n,r)+t}function Vl(e,t=0,n=1,r,i){e.min=Rc(e.min,t,n,r,i),e.max=Rc(e.max,t,n,r,i)}function Eh(e,{x:t,y:n}){Vl(e.x,t.translate,t.scale,t.originPoint),Vl(e.y,n.translate,n.scale,n.originPoint)}function c1(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let o,s;for(let l=0;l<i;l++){o=n[l],s=o.projectionDelta;const a=o.instance;a&&a.style&&a.style.display==="contents"||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&_n(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,Eh(e,s)),r&&qt(o.latestValues)&&_n(e,o.latestValues))}t.x=Dc(t.x),t.y=Dc(t.y)}function Dc(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Pt(e,t){e.min=e.min+t,e.max=e.max+t}function _c(e,t,[n,r,i]){const o=t[i]!==void 0?t[i]:.5,s=K(e.min,e.max,o);Vl(e,t[n],t[r],s,t.scale)}const f1=["x","scaleX","originX"],d1=["y","scaleY","originY"];function _n(e,t){_c(e.x,t,f1),_c(e.y,t,d1)}function Lh(e,t){return Ph(u1(e.getBoundingClientRect(),t))}function p1(e,t,n){const r=Lh(e,n),{scroll:i}=t;return i&&(Pt(r.x,i.offset.x),Pt(r.y,i.offset.y)),r}const Mh=({current:e})=>e?e.ownerDocument.defaultView:null,h1=new WeakMap;class m1{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=b(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=c=>{const{dragSnapToOrigin:f}=this.getProps();f?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(zo(c,"page").point)},o=(c,f)=>{const{drag:d,dragPropagation:g,onDragStart:y}=this.getProps();if(d&&!g&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Up(d),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ie(S=>{let m=this.getAxisMotionValue(S).get()||0;if(it.test(m)){const{projection:p}=this.visualElement;if(p&&p.layout){const h=p.layout.layoutBox[S];h&&(m=De(h)*(parseFloat(m)/100))}}this.originPoint[S]=m}),y&&$.update(()=>y(c,f),!1,!0);const{animationState:v}=this.visualElement;v&&v.setActive("whileDrag",!0)},s=(c,f)=>{const{dragPropagation:d,dragDirectionLock:g,onDirectionLock:y,onDrag:v}=this.getProps();if(!d&&!this.openGlobalLock)return;const{offset:S}=f;if(g&&this.currentDirection===null){this.currentDirection=g1(S),this.currentDirection!==null&&y&&y(this.currentDirection);return}this.updateAxis("x",f.point,S),this.updateAxis("y",f.point,S),this.visualElement.render(),v&&v(c,f)},l=(c,f)=>this.stop(c,f),a=()=>Ie(c=>{var f;return this.getAnimationState(c)==="paused"&&((f=this.getAxisMotionValue(c).animation)===null||f===void 0?void 0:f.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new kh(t,{onSessionStart:i,onStart:o,onMove:s,onSessionEnd:l,resumeAnimation:a},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Mh(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&$.update(()=>o(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Pi(t,i,this.currentDirection))return;const o=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=n1(s,this.constraints[t],this.elastic[t])),o.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,o=this.constraints;n&&jn(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=r1(i.layoutBox,n):this.constraints=!1,this.elastic=l1(r),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Ie(s=>{this.getAxisMotionValue(s)&&(this.constraints[s]=s1(i.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!jn(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=p1(r,i.root,this.visualElement.getTransformPagePoint());let s=i1(i.layout.layoutBox,o);if(n){const l=n(a1(s));this.hasMutatedConstraints=!!l,l&&(s=Ph(l))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:o,dragSnapToOrigin:s,onDragTransitionEnd:l}=this.getProps(),a=this.constraints||{},u=Ie(c=>{if(!Pi(c,n,this.currentDirection))return;let f=a&&a[c]||{};s&&(f={min:0,max:0});const d=i?200:1e6,g=i?40:1e7,y={type:"inertia",velocity:r?t[c]:0,bounceStiffness:d,bounceDamping:g,timeConstant:750,restDelta:1,restSpeed:10,...o,...f};return this.startAxisValueAnimation(c,y)});return Promise.all(u).then(l)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(Ga(t,r,0,n))}stopAnimation(){Ie(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Ie(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Ie(n=>{const{drag:r}=this.getProps();if(!Pi(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:s,max:l}=i.layout.layoutBox[n];o.set(t[n]-K(s,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!jn(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Ie(s=>{const l=this.getAxisMotionValue(s);if(l){const a=l.get();i[s]=o1({min:a,max:a},this.constraints[s])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Ie(s=>{if(!Pi(s,t,null))return;const l=this.getAxisMotionValue(s),{min:a,max:u}=this.constraints[s];l.set(K(a,u,i[s]))})}addListeners(){if(!this.visualElement.current)return;h1.set(this.visualElement,this);const t=this.visualElement.current,n=ft(t,"pointerdown",a=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(a)}),r=()=>{const{dragConstraints:a}=this.getProps();jn(a)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r();const s=ut(window,"resize",()=>this.scalePositionWithinConstraints()),l=i.addEventListener("didUpdate",({delta:a,hasLayoutChanged:u})=>{this.isDragging&&u&&(Ie(c=>{const f=this.getAxisMotionValue(c);f&&(this.originPoint[c]+=a[c].translate,f.set(f.get()+a[c].translate))}),this.visualElement.render())});return()=>{s(),n(),o(),l&&l()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:s=Ml,dragMomentum:l=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:o,dragElastic:s,dragMomentum:l}}}function Pi(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function g1(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class y1 extends Gt{constructor(t){super(t),this.removeGroupControls=q,this.removeListeners=q,this.controls=new m1(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||q}unmount(){this.removeGroupControls(),this.removeListeners()}}const Nc=e=>(t,n)=>{e&&$.update(()=>e(t,n))};class v1 extends Gt{constructor(){super(...arguments),this.removePointerDownListener=q}onPointerDown(t){this.session=new kh(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Mh(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:Nc(t),onStart:Nc(n),onMove:r,onEnd:(o,s)=>{delete this.session,i&&$.update(()=>i(o,s))}}}mount(){this.removePointerDownListener=ft(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function x1(){const e=A.useContext(Ma);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=A.useId();return A.useEffect(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}const Ii={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Fc(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const ar={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(V.test(e))e=parseFloat(e);else return e;const n=Fc(e,t.target.x),r=Fc(e,t.target.y);return`${n}% ${r}%`}},w1={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=Ut.parse(e);if(i.length>5)return r;const o=Ut.createTransformer(e),s=typeof i[0]!="number"?1:0,l=n.x.scale*t.x,a=n.y.scale*t.y;i[0+s]/=l,i[1+s]/=a;const u=K(l,a,.5);return typeof i[2+s]=="number"&&(i[2+s]/=u),typeof i[3+s]=="number"&&(i[3+s]/=u),o(i)}};class S1 extends Ol.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:o}=t;Ly(k1),o&&(n.group&&n.group.add(o),r&&r.register&&i&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Ii.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:o}=this.props,s=r.projection;return s&&(s.isPresent=o,i||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?s.promote():s.relegate()||$.postRender(()=>{const l=s.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Ah(e){const[t,n]=x1(),r=A.useContext(Pp);return Ol.createElement(S1,{...e,layoutGroup:r,switchLayoutGroup:A.useContext(Tp),isPresent:t,safeToRemove:n})}const k1={borderRadius:{...ar,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ar,borderTopRightRadius:ar,borderBottomLeftRadius:ar,borderBottomRightRadius:ar,boxShadow:w1},Vh=["TopLeft","TopRight","BottomLeft","BottomRight"],C1=Vh.length,Oc=e=>typeof e=="string"?parseFloat(e):e,Ic=e=>typeof e=="number"||V.test(e);function P1(e,t,n,r,i,o){i?(e.opacity=K(0,n.opacity!==void 0?n.opacity:1,T1(r)),e.opacityExit=K(t.opacity!==void 0?t.opacity:1,0,E1(r))):o&&(e.opacity=K(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<C1;s++){const l=`border${Vh[s]}Radius`;let a=Bc(t,l),u=Bc(n,l);if(a===void 0&&u===void 0)continue;a||(a=0),u||(u=0),a===0||u===0||Ic(a)===Ic(u)?(e[l]=Math.max(K(Oc(a),Oc(u),r),0),(it.test(u)||it.test(a))&&(e[l]+="%")):e[l]=u}(t.rotate||n.rotate)&&(e.rotate=K(t.rotate||0,n.rotate||0,r))}function Bc(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const T1=jh(0,.5,bp),E1=jh(.5,.95,q);function jh(e,t,n){return r=>r<e?0:r>t?1:n(Kr(e,t,r))}function zc(e,t){e.min=t.min,e.max=t.max}function Oe(e,t){zc(e.x,t.x),zc(e.y,t.y)}function Uc(e,t,n,r,i){return e-=t,e=yo(e,1/n,r),i!==void 0&&(e=yo(e,1/i,r)),e}function L1(e,t=0,n=1,r=.5,i,o=e,s=e){if(it.test(t)&&(t=parseFloat(t),t=K(s.min,s.max,t/100)-s.min),typeof t!="number")return;let l=K(o.min,o.max,r);e===o&&(l-=t),e.min=Uc(e.min,t,n,l,i),e.max=Uc(e.max,t,n,l,i)}function $c(e,t,[n,r,i],o,s){L1(e,t[n],t[r],t[i],t.scale,o,s)}const M1=["x","scaleX","originX"],A1=["y","scaleY","originY"];function Wc(e,t,n,r){$c(e.x,t,M1,n?n.x:void 0,r?r.x:void 0),$c(e.y,t,A1,n?n.y:void 0,r?r.y:void 0)}function Hc(e){return e.translate===0&&e.scale===1}function Rh(e){return Hc(e.x)&&Hc(e.y)}function V1(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function Dh(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function Gc(e){return De(e.x)/De(e.y)}class j1{constructor(){this.members=[]}add(t){Ka(this.members,t),t.scheduleRender()}remove(t){if(Qa(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Kc(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(r=`translate3d(${i}px, ${o}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:a,rotateX:u,rotateY:c}=n;a&&(r+=`rotate(${a}deg) `),u&&(r+=`rotateX(${u}deg) `),c&&(r+=`rotateY(${c}deg) `)}const s=e.x.scale*t.x,l=e.y.scale*t.y;return(s!==1||l!==1)&&(r+=`scale(${s}, ${l})`),r||"none"}const R1=(e,t)=>e.depth-t.depth;class D1{constructor(){this.children=[],this.isDirty=!1}add(t){Ka(this.children,t),this.isDirty=!0}remove(t){Qa(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(R1),this.isDirty=!1,this.children.forEach(t)}}function _1(e,t){const n=performance.now(),r=({timestamp:i})=>{const o=i-n;o>=t&&(yt(r),e(o-t))};return $.read(r,!0),()=>yt(r)}function N1(e){window.MotionDebug&&window.MotionDebug.record(e)}function F1(e){return e instanceof SVGElement&&e.tagName!=="svg"}function O1(e,t,n){const r=Le(e)?e:Xn(e);return r.start(Ga("",r,t,n)),r.animation}const Qc=["","X","Y","Z"],I1={visibility:"hidden"},Xc=1e3;let B1=0;const bt={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function _h({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(s={},l=t==null?void 0:t()){this.id=B1++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,bt.totalNodes=bt.resolvedTargetDeltas=bt.recalculatedProjection=0,this.nodes.forEach($1),this.nodes.forEach(Q1),this.nodes.forEach(X1),this.nodes.forEach(W1),N1(bt)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new D1)}addEventListener(s,l){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new Xa),this.eventHandlers.get(s).add(l)}notifyListeners(s,...l){const a=this.eventHandlers.get(s);a&&a.notify(...l)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,l=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=F1(s),this.instance=s;const{layoutId:a,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),l&&(u||a)&&(this.isLayoutDirty=!0),e){let f;const d=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,f&&f(),f=_1(d,250),Ii.hasAnimatedSinceResize&&(Ii.hasAnimatedSinceResize=!1,this.nodes.forEach(Zc))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&c&&(a||u)&&this.addEventListener("didUpdate",({delta:f,hasLayoutChanged:d,hasRelativeTargetChanged:g,layout:y})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const v=this.options.transition||c.getDefaultTransition()||b1,{onLayoutAnimationStart:S,onLayoutAnimationComplete:m}=c.getProps(),p=!this.targetLayout||!Dh(this.targetLayout,y)||g,h=!d&&g;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||h||d&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(f,h);const x={...Ha(v,"layout"),onPlay:S,onComplete:m};(c.shouldReduceMotion||this.options.layoutRoot)&&(x.delay=0,x.type=!1),this.startAnimation(x)}else d||Zc(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=y})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,yt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Y1),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const f=this.path[c];f.shouldResetTransform=!0,f.updateScroll("snapshot"),f.options.layoutRoot&&f.willUpdate(!1)}const{layoutId:l,layout:a}=this.options;if(l===void 0&&!a)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Yc);return}this.isUpdating||this.nodes.forEach(G1),this.isUpdating=!1,this.nodes.forEach(K1),this.nodes.forEach(z1),this.nodes.forEach(U1),this.clearAllSnapshots();const l=performance.now();pe.delta=zt(0,1e3/60,l-pe.timestamp),pe.timestamp=l,pe.isProcessing=!0,ms.update.process(pe),ms.preRender.process(pe),ms.render.process(pe),pe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(H1),this.sharedNodes.forEach(Z1)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,$.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){$.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=b(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let l=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(l=!1),l&&(this.scroll={animationId:this.root.animationId,phase:s,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const s=this.isLayoutDirty||this.shouldResetTransform,l=this.projectionDelta&&!Rh(this.projectionDelta),a=this.getTransformTemplate(),u=a?a(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;s&&(l||qt(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const l=this.measurePageBox();let a=this.removeElementScroll(l);return s&&(a=this.removeTransform(a)),ex(a),{animationId:this.root.animationId,measuredBox:l,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:s}=this.options;if(!s)return b();const l=s.measureViewportBox(),{scroll:a}=this.root;return a&&(Pt(l.x,a.offset.x),Pt(l.y,a.offset.y)),l}removeElementScroll(s){const l=b();Oe(l,s);for(let a=0;a<this.path.length;a++){const u=this.path[a],{scroll:c,options:f}=u;if(u!==this.root&&c&&f.layoutScroll){if(c.isRoot){Oe(l,s);const{scroll:d}=this.root;d&&(Pt(l.x,-d.offset.x),Pt(l.y,-d.offset.y))}Pt(l.x,c.offset.x),Pt(l.y,c.offset.y)}}return l}applyTransform(s,l=!1){const a=b();Oe(a,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];!l&&c.options.layoutScroll&&c.scroll&&c!==c.root&&_n(a,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),qt(c.latestValues)&&_n(a,c.latestValues)}return qt(this.latestValues)&&_n(a,this.latestValues),a}removeTransform(s){const l=b();Oe(l,s);for(let a=0;a<this.path.length;a++){const u=this.path[a];if(!u.instance||!qt(u.latestValues))continue;Al(u.latestValues)&&u.updateSnapshot();const c=b(),f=u.measurePageBox();Oe(c,f),Wc(l,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return qt(this.latestValues)&&Wc(l,this.latestValues),l}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==pe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var l;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==a;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:f,layoutId:d}=this.options;if(!(!this.layout||!(f||d))){if(this.resolvedRelativeTargetAt=pe.timestamp,!this.targetDelta&&!this.relativeTarget){const g=this.getClosestProjectingParent();g&&g.layout&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=b(),this.relativeTargetOrigin=b(),Er(this.relativeTargetOrigin,this.layout.layoutBox,g.layout.layoutBox),Oe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=b(),this.targetWithTransforms=b()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),t1(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Oe(this.target,this.layout.layoutBox),Eh(this.target,this.targetDelta)):Oe(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const g=this.getClosestProjectingParent();g&&!!g.resumingFrom==!!this.resumingFrom&&!g.options.layoutScroll&&g.target&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=b(),this.relativeTargetOrigin=b(),Er(this.relativeTargetOrigin,this.target,g.target),Oe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}bt.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Al(this.parent.latestValues)||Th(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const l=this.getLead(),a=!!this.resumingFrom||this!==l;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===pe.timestamp&&(u=!1),u)return;const{layout:c,layoutId:f}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||f))return;Oe(this.layoutCorrected,this.layout.layoutBox);const d=this.treeScale.x,g=this.treeScale.y;c1(this.layoutCorrected,this.treeScale,this.path,a),l.layout&&!l.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(l.target=l.layout.layoutBox);const{target:y}=l;if(!y){this.projectionTransform&&(this.projectionDelta=Dn(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=Dn(),this.projectionDeltaWithTransform=Dn());const v=this.projectionTransform;Tr(this.projectionDelta,this.layoutCorrected,y,this.latestValues),this.projectionTransform=Kc(this.projectionDelta,this.treeScale),(this.projectionTransform!==v||this.treeScale.x!==d||this.treeScale.y!==g)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",y)),bt.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),s){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(s,l=!1){const a=this.snapshot,u=a?a.latestValues:{},c={...this.latestValues},f=Dn();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const d=b(),g=a?a.source:void 0,y=this.layout?this.layout.source:void 0,v=g!==y,S=this.getStack(),m=!S||S.members.length<=1,p=!!(v&&!m&&this.options.crossfade===!0&&!this.path.some(q1));this.animationProgress=0;let h;this.mixTargetDelta=x=>{const k=x/1e3;Jc(f.x,s.x,k),Jc(f.y,s.y,k),this.setTargetDelta(f),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Er(d,this.layout.layoutBox,this.relativeParent.layout.layoutBox),J1(this.relativeTarget,this.relativeTargetOrigin,d,k),h&&V1(this.relativeTarget,h)&&(this.isProjectionDirty=!1),h||(h=b()),Oe(h,this.relativeTarget)),v&&(this.animationValues=c,P1(c,u,this.latestValues,k,p,m)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=k},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(yt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=$.update(()=>{Ii.hasAnimatedSinceResize=!0,this.currentAnimation=O1(0,Xc,{...s,onUpdate:l=>{this.mixTargetDelta(l),s.onUpdate&&s.onUpdate(l)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Xc),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:l,target:a,layout:u,latestValues:c}=s;if(!(!l||!a||!u)){if(this!==s&&this.layout&&u&&Nh(this.options.animationType,this.layout.layoutBox,u.layoutBox)){a=this.target||b();const f=De(this.layout.layoutBox.x);a.x.min=s.target.x.min,a.x.max=a.x.min+f;const d=De(this.layout.layoutBox.y);a.y.min=s.target.y.min,a.y.max=a.y.min+d}Oe(l,a),_n(l,c),Tr(this.projectionDeltaWithTransform,this.layoutCorrected,l,c)}}registerSharedNode(s,l){this.sharedNodes.has(s)||this.sharedNodes.set(s,new j1),this.sharedNodes.get(s).add(l);const u=l.options.initialPromotionConfig;l.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(l):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:l}=this.options;return l?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:l}=this.options;return l?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:l,preserveFollowOpacity:a}={}){const u=this.getStack();u&&u.promote(this,a),s&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetRotation(){const{visualElement:s}=this.options;if(!s)return;let l=!1;const{latestValues:a}=s;if((a.rotate||a.rotateX||a.rotateY||a.rotateZ)&&(l=!0),!l)return;const u={};for(let c=0;c<Qc.length;c++){const f="rotate"+Qc[c];a[f]&&(u[f]=a[f],s.setStaticValue(f,0))}s.render();for(const c in u)s.setStaticValue(c,u[c]);s.scheduleRender()}getProjectionStyles(s){var l,a;if(!this.instance||this.isSVG)return;if(!this.isVisible)return I1;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=Oi(s==null?void 0:s.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const f=this.getLead();if(!this.projectionDelta||!this.layout||!f.target){const v={};return this.options.layoutId&&(v.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,v.pointerEvents=Oi(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!qt(this.latestValues)&&(v.transform=c?c({},""):"none",this.hasProjected=!1),v}const d=f.animationValues||f.latestValues;this.applyTransformsToTarget(),u.transform=Kc(this.projectionDeltaWithTransform,this.treeScale,d),c&&(u.transform=c(d,u.transform));const{x:g,y}=this.projectionDelta;u.transformOrigin=`${g.origin*100}% ${y.origin*100}% 0`,f.animationValues?u.opacity=f===this?(a=(l=d.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&a!==void 0?a:1:this.preserveOpacity?this.latestValues.opacity:d.opacityExit:u.opacity=f===this?d.opacity!==void 0?d.opacity:"":d.opacityExit!==void 0?d.opacityExit:0;for(const v in uo){if(d[v]===void 0)continue;const{correct:S,applyTo:m}=uo[v],p=u.transform==="none"?d[v]:S(d[v],f);if(m){const h=m.length;for(let x=0;x<h;x++)u[m[x]]=p}else u[v]=p}return this.options.layoutId&&(u.pointerEvents=f===this?Oi(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var l;return(l=s.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(Yc),this.root.sharedNodes.clear()}}}function z1(e){e.updateLayout()}function U1(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:o}=e.options,s=n.source!==e.layout.source;o==="size"?Ie(f=>{const d=s?n.measuredBox[f]:n.layoutBox[f],g=De(d);d.min=r[f].min,d.max=d.min+g}):Nh(o,n.layoutBox,r)&&Ie(f=>{const d=s?n.measuredBox[f]:n.layoutBox[f],g=De(r[f]);d.max=d.min+g,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[f].max=e.relativeTarget[f].min+g)});const l=Dn();Tr(l,r,n.layoutBox);const a=Dn();s?Tr(a,e.applyTransform(i,!0),n.measuredBox):Tr(a,r,n.layoutBox);const u=!Rh(l);let c=!1;if(!e.resumeFrom){const f=e.getClosestProjectingParent();if(f&&!f.resumeFrom){const{snapshot:d,layout:g}=f;if(d&&g){const y=b();Er(y,n.layoutBox,d.layoutBox);const v=b();Er(v,r,g.layoutBox),Dh(y,v)||(c=!0),f.options.layoutRoot&&(e.relativeTarget=v,e.relativeTargetOrigin=y,e.relativeParent=f)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:a,layoutDelta:l,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function $1(e){bt.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function W1(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function H1(e){e.clearSnapshot()}function Yc(e){e.clearMeasurements()}function G1(e){e.isLayoutDirty=!1}function K1(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Zc(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Q1(e){e.resolveTargetDelta()}function X1(e){e.calcProjection()}function Y1(e){e.resetRotation()}function Z1(e){e.removeLeadSnapshot()}function Jc(e,t,n){e.translate=K(t.translate,0,n),e.scale=K(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function qc(e,t,n,r){e.min=K(t.min,n.min,r),e.max=K(t.max,n.max,r)}function J1(e,t,n,r){qc(e.x,t.x,n.x,r),qc(e.y,t.y,n.y,r)}function q1(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const b1={duration:.45,ease:[.4,0,.1,1]},bc=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),ef=bc("applewebkit/")&&!bc("chrome/")?Math.round:q;function tf(e){e.min=ef(e.min),e.max=ef(e.max)}function ex(e){tf(e.x),tf(e.y)}function Nh(e,t,n){return e==="position"||e==="preserve-aspect"&&!Ll(Gc(t),Gc(n),.2)}const tx=_h({attachResizeListener:(e,t)=>ut(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Es={current:void 0},Fh=_h({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Es.current){const e=new tx({});e.mount(window),e.setOptions({layoutScroll:!0}),Es.current=e}return Es.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),nx={pan:{Feature:v1},drag:{Feature:y1,ProjectionNode:Fh,MeasureLayout:Ah}},rx=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function ix(e){const t=rx.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function jl(e,t,n=1){const[r,i]=ix(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);if(o){const s=o.trim();return vh(s)?parseFloat(s):s}else return wl(i)?jl(i,t,n+1):i}function ox(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(i=>{const o=i.get();if(!wl(o))return;const s=jl(o,r);s&&i.set(s)});for(const i in t){const o=t[i];if(!wl(o))continue;const s=jl(o,r);s&&(t[i]=s,n||(n={}),n[i]===void 0&&(n[i]=o))}return{target:t,transitionEnd:n}}const sx=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),Oh=e=>sx.has(e),lx=e=>Object.keys(e).some(Oh),nf=e=>e===gn||e===V,rf=(e,t)=>parseFloat(e.split(", ")[t]),of=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return rf(i[1],t);{const o=r.match(/^matrix\((.+)\)$/);return o?rf(o[1],e):0}},ax=new Set(["x","y","z"]),ux=br.filter(e=>!ax.has(e));function cx(e){const t=[];return ux.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const Yn={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:of(4,13),y:of(5,14)};Yn.translateX=Yn.x;Yn.translateY=Yn.y;const fx=(e,t,n)=>{const r=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:s}=o,l={};s==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{l[u]=Yn[u](r,o)}),t.render();const a=t.measureViewportBox();return n.forEach(u=>{const c=t.getValue(u);c&&c.jump(l[u]),e[u]=Yn[u](a,o)}),e},dx=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter(Oh);let o=[],s=!1;const l=[];if(i.forEach(a=>{const u=e.getValue(a);if(!e.hasValue(a))return;let c=n[a],f=lr(c);const d=t[a];let g;if(fo(d)){const y=d.length,v=d[0]===null?1:0;c=d[v],f=lr(c);for(let S=v;S<y&&d[S]!==null;S++)g?Ba(lr(d[S])===g):g=lr(d[S])}else g=lr(d);if(f!==g)if(nf(f)&&nf(g)){const y=u.get();typeof y=="string"&&u.set(parseFloat(y)),typeof d=="string"?t[a]=parseFloat(d):Array.isArray(d)&&g===V&&(t[a]=d.map(parseFloat))}else f!=null&&f.transform&&(g!=null&&g.transform)&&(c===0||d===0)?c===0?u.set(g.transform(c)):t[a]=f.transform(d):(s||(o=cx(e),s=!0),l.push(a),r[a]=r[a]!==void 0?r[a]:t[a],u.jump(d))}),l.length){const a=l.indexOf("height")>=0?window.pageYOffset:null,u=fx(t,e,l);return o.length&&o.forEach(([c,f])=>{e.getValue(c).set(f)}),e.render(),Fo&&a!==null&&window.scrollTo({top:a}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function px(e,t,n,r){return lx(t)?dx(e,t,n,r):{target:t,transitionEnd:r}}const hx=(e,t,n,r)=>{const i=ox(e,t,r);return t=i.target,r=i.transitionEnd,px(e,t,n,r)},Rl={current:null},Ih={current:!1};function mx(){if(Ih.current=!0,!!Fo)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Rl.current=e.matches;e.addListener(t),t()}else Rl.current=!1}function gx(e,t,n){const{willChange:r}=t;for(const i in t){const o=t[i],s=n[i];if(Le(o))e.addValue(i,o),go(r)&&r.add(i);else if(Le(s))e.addValue(i,Xn(o,{owner:e})),go(r)&&r.remove(i);else if(s!==o)if(e.hasValue(i)){const l=e.getValue(i);!l.hasAnimated&&l.set(o)}else{const l=e.getStaticValue(i);e.addValue(i,Xn(l!==void 0?l:o,{owner:e}))}}for(const i in n)t[i]===void 0&&e.removeValue(i);return t}const sf=new WeakMap,Bh=Object.keys(Gr),yx=Bh.length,lf=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],vx=ja.length;class xx{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>$.render(this.render,!1,!0);const{latestValues:l,renderState:a}=o;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=a,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.isControllingVariants=Io(n),this.isVariantNode=Cp(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(n,{});for(const f in c){const d=c[f];l[f]!==void 0&&Le(d)&&(d.set(l[f],!1),go(u)&&u.add(f))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,sf.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Ih.current||mx(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Rl.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){sf.delete(this.current),this.projection&&this.projection.unmount(),yt(this.notifyUpdate),yt(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=mn.has(t),i=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&$.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),o()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,i,o){let s,l;for(let a=0;a<yx;a++){const u=Bh[a],{isEnabled:c,Feature:f,ProjectionNode:d,MeasureLayout:g}=Gr[u];d&&(s=d),c(n)&&(!this.features[u]&&f&&(this.features[u]=new f(this)),g&&(l=g))}if((this.type==="html"||this.type==="svg")&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);const{layoutId:a,layout:u,drag:c,dragConstraints:f,layoutScroll:d,layoutRoot:g}=n;this.projection.setOptions({layoutId:a,layout:u,alwaysMeasureLayout:!!c||f&&jn(f),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:o,layoutScroll:d,layoutRoot:g})}return l}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):b()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<lf.length;r++){const i=lf[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o=t["on"+i];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=gx(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<vx;r++){const i=ja[r],o=this.props[i];(Hr(o)||o===!1)&&(n[i]=o)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Xn(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=Ia(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!Le(o)?o:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new Xa),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class zh extends xx{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:i},o){let s=F0(r,t||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),s&&(s=i(s))),o){_0(this,r,s);const l=hx(this,r,s,n);n=l.transitionEnd,r=l.target}return{transition:t,transitionEnd:n,...r}}}function wx(e){return window.getComputedStyle(e)}class Sx extends zh{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(mn.has(n)){const r=Wa(n);return r&&r.default||0}else{const r=wx(t),i=(Mp(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Lh(t,n)}build(t,n,r,i){Da(t,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,n){return Oa(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Le(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,i){_p(t,n,r,i)}}class kx extends zh{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(mn.has(n)){const r=Wa(n);return r&&r.default||0}return n=Np.has(n)?n:Aa(n),t.getAttribute(n)}measureInstanceViewportBox(){return b()}scrapeMotionValuesFromProps(t,n){return Op(t,n)}build(t,n,r,i){Na(t,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,n,r,i){Fp(t,n,r,i)}mount(t){this.isSVGTag=Fa(t.tagName),super.mount(t)}}const Cx=(e,t)=>Ra(e)?new kx(t,{enableHardwareAcceleration:!1}):new Sx(t,{enableHardwareAcceleration:!0}),Px={layout:{ProjectionNode:Fh,MeasureLayout:Ah}},Tx={...J0,...yv,...nx,...Px},vo=Ty((e,t)=>iv(e,t,Tx,Cx)),Qr={BEE_DIRECTIONS:["N","NE","E","SE","S","SW","W","NW"],PHEROMONE_LEVELS:[1,2,3,4,5],WEB_STRUCTURE:{center:[2,2],rings:[[[2,2]],[[1,1],[1,2],[1,3],[2,1],[2,3],[3,1],[3,2],[3,3]],[[0,0],[0,1],[0,2],[0,3],[0,4],[1,0],[1,4],[2,0],[2,4],[3,0],[3,4],[4,0],[4,1],[4,2],[4,3],[4,4]]]},letterToBeeAngle:e=>(e.charCodeAt(0)-65)*45%360,letterToPheromone:e=>(e.charCodeAt(0)-65)%5+1,getWebPosition:e=>{const t=Qr.WEB_STRUCTURE.rings;let n=0;for(let r=0;r<t.length;r++){if(e<n+t[r].length)return{ring:r,position:t[r][e-n],isCenter:r===0};n+=t[r].length}return{ring:2,position:[e%5,Math.floor(e/5)],isCenter:!1}}};function Ex({message:e,gridSize:t}){const[n,r]=A.useState([]),[i,o]=A.useState([]);A.useEffect(()=>{const l=t*t,a=e.toUpperCase().replace(/[^A-Z]/g,"").padEnd(l,"X").slice(0,l);let u=Array(l).fill(""),c=[];for(let f=0;f<a.length;f++){const d=a[f],g=Math.floor(f/t),y=f%t,v=g*t+y,S=Qr.letterToBeeAngle(d),m=Qr.letterToPheromone(d);u[v]=d,c.push({letter:d,beeAngle:S,pheromoneLevel:m,gridIndex:v,row:g,col:y})}r(u),o(c)},[e,t]);const s=l=>{const a=i.find(h=>h.gridIndex===l),u=n[l]&&n[l]!=="",c=Math.floor(l/t),f=l%t,d=Math.floor(t/2),g=c===d&&f===d,y=Math.abs(c-d)+Math.abs(f-d);let v="#fef3c7",S="#f59e0b",m="2px";g?(v="#dc2626",S="#991b1b",m="3px"):y===1?(v="#16a34a",S="#15803d"):u&&a&&(v=`rgba(34, 197, 94, ${a.pheromoneLevel/5})`);const p=Math.max(30,Math.min(60,300/t));return{width:`${p}px`,height:`${p}px`,display:"flex",alignItems:"center",justifyContent:"center",border:`${m} solid ${S}`,borderRadius:g?"50%":"8px",backgroundColor:v,color:g?"white":"#92400e",fontWeight:"bold",fontSize:`${Math.max(10,p/3)}px`,position:"relative"}};return w.jsxs("div",{children:[w.jsx("div",{style:{display:"grid",gridTemplateColumns:`repeat(${t}, 1fr)`,gap:"4px",marginTop:"20px",maxWidth:`${Math.min(400,t*70)}px`,margin:"20px auto"},children:n.map((l,a)=>{const u=i.find(c=>c.gridIndex===a);return w.jsxs(vo.div,{style:s(a),initial:{scale:0,rotate:u?u.beeAngle:0},animate:{scale:1,rotate:0},transition:{delay:a*.1,type:"spring",stiffness:200,damping:10},title:u?`🐝 Angle: ${u.beeAngle}° | 🐜 Pheromone: ${u.pheromoneLevel}`:"",children:[l,u&&w.jsx("div",{style:{position:"absolute",top:"-8px",right:"-8px",width:"16px",height:"16px",borderRadius:"50%",backgroundColor:"#dc2626",color:"white",fontSize:"10px",display:"flex",alignItems:"center",justifyContent:"center"},children:u.pheromoneLevel})]},a)})}),w.jsxs("div",{style:{marginTop:"20px",padding:"15px",backgroundColor:"#f9fafb",borderRadius:"8px",fontSize:"12px"},children:[w.jsxs("div",{children:[w.jsx("strong",{children:"🕷️ Spider Web:"})," Red center, green inner ring"]}),w.jsxs("div",{children:[w.jsx("strong",{children:"🐝 Bee Dance:"})," Rotation angle encodes letter"]}),w.jsxs("div",{children:[w.jsx("strong",{children:"🐜 Ant Trails:"})," Pheromone strength (1-5) in red circles"]})]})]})}function Lx({message:e,gridSize:t}){const[n,r]=A.useState([]),[i,o]=A.useState(""),[s,l]=A.useState([]);return A.useEffect(()=>{const a=e.toUpperCase().replace(/[^A-Z]/g,""),u=t*t;if(a.length===0){r(Array(u).fill("")),l([]),o("");return}let c=Array.from({length:t},()=>Array(t).fill("")),f=[],d=0;const g=Math.floor(t/2);d<a.length&&(c[g][g]=a[d++]);for(let S=1;S<=g;S++){let m=[];for(let p=0;p<t;p++)for(let h=0;h<t;h++)Math.max(Math.abs(p-g),Math.abs(h-g))===S&&m.push([p,h]);m.sort((p,h)=>{const[x,k]=p,[E,T]=h,P=Math.atan2(x-g,k-g),j=Math.atan2(E-g,T-g);return P-j});for(let[p,h]of m)d<a.length&&(c[p][h]=a[d++])}let y="",v=Array(u).fill("");for(let S=0;S<t;S++)for(let m=0;m<t;m++){const p=c[S][m]||"",h=S*t+m;v[h]=p,p&&(y+=p,f.push({letter:p,beeAngle:Qr.letterToBeeAngle(p),pheromoneLevel:Qr.letterToPheromone(p),isValidPosition:!0,gridIndex:h}))}r(v),l(f),o(y.replace(/X+$/,""))},[e,t]),w.jsxs("div",{children:[w.jsx("div",{style:{display:"grid",gridTemplateColumns:`repeat(${t}, 1fr)`,gap:"4px",marginTop:"20px",maxWidth:`${Math.min(400,t*70)}px`,margin:"20px auto"},children:n.map((a,u)=>{const c=Math.floor(u/t),f=u%t,d=Math.floor(t/2),g=c===d&&f===d,y=Math.abs(c-d)+Math.abs(f-d),v=Math.max(30,Math.min(60,300/t));let S="#dbeafe",m="#3b82f6";return g?(S="#7c3aed",m="#5b21b6"):y===1&&(S="#059669",m="#047857"),w.jsx(vo.div,{style:{width:`${v}px`,height:`${v}px`,display:"flex",alignItems:"center",justifyContent:"center",border:`2px solid ${m}`,borderRadius:g?"50%":"8px",backgroundColor:S,color:g||y===1?"white":"#1e40af",fontWeight:"bold",fontSize:`${Math.max(10,v/3)}px`},initial:{scale:0,rotate:180},animate:{scale:1,rotate:0},transition:{delay:u*.08,type:"spring",stiffness:150,damping:12},children:a},u)})}),i&&w.jsx(vo.div,{style:{marginTop:"20px",padding:"15px",backgroundColor:"#dcfce7",border:"2px solid #16a34a",borderRadius:"8px",textAlign:"center"},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:2.5},children:w.jsxs("strong",{style:{color:"#15803d"},children:["Decrypted Message: ",i]})})]})}function Mx({isVisible:e,onToggle:t}){return e?w.jsxs(vo.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},style:{backgroundColor:"#f8fafc",border:"2px solid #e2e8f0",borderRadius:"12px",padding:"20px",marginBottom:"30px",fontSize:"14px",lineHeight:"1.6"},children:[w.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"15px"},children:[w.jsx("h3",{style:{color:"#1e293b",margin:0},children:"🧠 How the Bio-Inspired Cipher Works"}),w.jsx("button",{onClick:t,style:{padding:"5px 10px",backgroundColor:"#ef4444",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"12px"},children:"✕ Close"})]}),w.jsxs("div",{style:{display:"grid",gap:"20px"},children:[w.jsxs("div",{style:{backgroundColor:"#fef3c7",padding:"15px",borderRadius:"8px",border:"1px solid #f59e0b"},children:[w.jsx("h4",{style:{color:"#92400e",marginTop:0},children:"🔐 ENCRYPTION PROCESS"}),w.jsxs("div",{style:{marginLeft:"10px"},children:[w.jsx("p",{children:w.jsx("strong",{children:"Step 1: Message Preparation"})}),w.jsxs("ul",{style:{marginLeft:"20px"},children:[w.jsx("li",{children:"Your message is cleaned (only letters A-Z kept)"}),w.jsx("li",{children:"Padded with 'X' characters to fill the grid completely"}),w.jsx("li",{children:'Example: "HELLO" → "HELLOX..." for a 5×5 grid (25 characters)'})]}),w.jsx("p",{children:w.jsx("strong",{children:"Step 2: Grid Filling (Like Ants Building)"})}),w.jsxs("ul",{style:{marginLeft:"20px"},children:[w.jsxs("li",{children:["Letters are placed in the grid ",w.jsx("em",{children:"row by row"}),", left to right"]}),w.jsx("li",{children:"Just like ants systematically building their nest"}),w.jsx("li",{children:"Each letter gets bio-inspired properties:"}),w.jsxs("li",{style:{marginLeft:"20px"},children:["🐝 ",w.jsx("strong",{children:"Bee Angle:"})," Each letter has a dance direction (A=0°, B=45°, etc.)"]}),w.jsxs("li",{style:{marginLeft:"20px"},children:["🐜 ",w.jsx("strong",{children:"Pheromone Level:"})," Strength from 1-5 based on the letter"]})]}),w.jsx("p",{children:w.jsx("strong",{children:"Step 3: Spider Web Reading Pattern"})}),w.jsxs("ul",{style:{marginLeft:"20px"},children:[w.jsxs("li",{children:["🕷️ ",w.jsx("strong",{children:"Start from center:"})," Like a spider beginning its web"]}),w.jsxs("li",{children:["📍 ",w.jsx("strong",{children:"Expand in rings:"})," Read outward in concentric circles"]}),w.jsxs("li",{children:["🔄 ",w.jsx("strong",{children:"Clockwise spiral:"})," Within each ring, go clockwise"]}),w.jsx("li",{children:"This creates the encrypted message by reading in spider-web order!"})]})]})]}),w.jsxs("div",{style:{backgroundColor:"#dbeafe",padding:"15px",borderRadius:"8px",border:"1px solid #3b82f6"},children:[w.jsx("h4",{style:{color:"#1e40af",marginTop:0},children:"🔓 DECRYPTION PROCESS"}),w.jsxs("div",{style:{marginLeft:"10px"},children:[w.jsx("p",{children:w.jsx("strong",{children:"Step 1: Reverse Spider Web Placement"})}),w.jsxs("ul",{style:{marginLeft:"20px"},children:[w.jsx("li",{children:"Take the encrypted message and place it back using spider web pattern"}),w.jsxs("li",{children:["🕷️ ",w.jsx("strong",{children:"First character:"})," Goes to center of the web"]}),w.jsxs("li",{children:["📍 ",w.jsx("strong",{children:"Next characters:"})," Fill rings outward (center → ring 1 → ring 2...)"]}),w.jsxs("li",{children:["🔄 ",w.jsx("strong",{children:"Clockwise order:"})," Within each ring, place clockwise"]})]}),w.jsx("p",{children:w.jsx("strong",{children:"Step 2: Row-by-Row Reading"})}),w.jsxs("ul",{style:{marginLeft:"20px"},children:[w.jsx("li",{children:"Once the grid is filled using spider pattern, read it normally"}),w.jsxs("li",{children:["📖 ",w.jsx("strong",{children:"Left to right, top to bottom"})," (like reading a book)"]}),w.jsx("li",{children:"This reverses the encryption and reveals the original message!"})]}),w.jsx("p",{children:w.jsx("strong",{children:"Step 3: Clean Up"})}),w.jsxs("ul",{style:{marginLeft:"20px"},children:[w.jsx("li",{children:"Remove trailing 'X' characters that were padding"}),w.jsxs("li",{children:["🎉 ",w.jsx("strong",{children:"Result:"})," Your original message is revealed!"]})]})]})]}),w.jsxs("div",{style:{backgroundColor:"#f0fdf4",padding:"15px",borderRadius:"8px",border:"1px solid #16a34a"},children:[w.jsx("h4",{style:{color:"#15803d",marginTop:0},children:"📋 SIMPLE EXAMPLE"}),w.jsxs("div",{style:{marginLeft:"10px"},children:[w.jsxs("p",{children:[w.jsx("strong",{children:"Message:"}),' "HELLO" (using 3×3 grid)']}),w.jsxs("div",{style:{display:"flex",gap:"20px",flexWrap:"wrap",alignItems:"flex-start"},children:[w.jsxs("div",{children:[w.jsx("p",{children:w.jsx("strong",{children:"1. Fill row by row:"})}),w.jsxs("div",{style:{fontFamily:"monospace",backgroundColor:"white",padding:"10px",borderRadius:"4px"},children:["H E L",w.jsx("br",{}),"L O X",w.jsx("br",{}),"X X X"]})]}),w.jsxs("div",{children:[w.jsx("p",{children:w.jsx("strong",{children:"2. Read spider-web style:"})}),w.jsxs("div",{style:{fontFamily:"monospace",backgroundColor:"white",padding:"10px",borderRadius:"4px"},children:["3 1 4",w.jsx("br",{}),"2 🕷️ 5",w.jsx("br",{}),"8 7 6"]}),w.jsx("p",{style:{fontSize:"12px",margin:"5px 0"},children:"🕷️ = center (start here)"})]}),w.jsxs("div",{children:[w.jsx("p",{children:w.jsx("strong",{children:"3. Encrypted result:"})}),w.jsx("div",{style:{fontFamily:"monospace",backgroundColor:"white",padding:"10px",borderRadius:"4px",fontWeight:"bold",color:"#dc2626"},children:"OHELXLXXX"}),w.jsx("p",{style:{fontSize:"12px",margin:"5px 0"},children:"Reading: O(center) → H,E,L,L(ring1) → X,X,X,X(ring2)"})]})]})]})]}),w.jsxs("div",{style:{backgroundColor:"#fdf4ff",padding:"15px",borderRadius:"8px",border:"1px solid #a855f7"},children:[w.jsx("h4",{style:{color:"#7c3aed",marginTop:0},children:"🌿 WHY BIO-INSPIRED?"}),w.jsxs("div",{style:{marginLeft:"10px"},children:[w.jsxs("p",{children:[w.jsx("strong",{children:"🕷️ Spider Web Construction:"})," Spiders build from center outward in organized patterns"]}),w.jsxs("p",{children:[w.jsx("strong",{children:"🐝 Bee Waggle Dance:"})," Bees communicate direction through dance angles"]}),w.jsxs("p",{children:[w.jsx("strong",{children:"🐜 Ant Pheromone Trails:"})," Ants leave chemical signals with different strengths"]}),w.jsx("p",{style:{fontStyle:"italic",color:"#6b46c1"},children:"Nature's communication methods inspire our encryption patterns, making them both beautiful and secure!"})]})]})]})]}):w.jsx("div",{style:{textAlign:"center",marginBottom:"20px"},children:w.jsx("button",{onClick:t,style:{padding:"10px 20px",backgroundColor:"#3b82f6",color:"white",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold"},children:"📚 How Does This Cipher Work?"})})}function Ax(){const[e,t]=A.useState("MEETATNOON"),[n,r]=A.useState("encrypt"),[i,o]=A.useState(5),[s,l]=A.useState(!1),a=()=>{if(n!=="encrypt")return"";const u=i*i,c=e.toUpperCase().replace(/[^A-Z]/g,"").padEnd(u,"X").slice(0,u);let f=Array.from({length:i},()=>Array(i).fill("")),d=0;for(let S=0;S<i;S++)for(let m=0;m<i;m++)d<c.length&&(f[S][m]=c[d++]);let g="";const y=Math.floor(i/2);let v=Array.from({length:i},()=>Array(i).fill(!1));f[y][y]&&(g+=f[y][y],v[y][y]=!0);for(let S=1;S<=y;S++){let m=[];for(let p=0;p<i;p++)for(let h=0;h<i;h++)Math.max(Math.abs(p-y),Math.abs(h-y))===S&&!v[p][h]&&m.push([p,h]);m.sort((p,h)=>{const[x,k]=p,[E,T]=h,P=Math.atan2(x-y,k-y),j=Math.atan2(E-y,T-y);return P-j});for(let[p,h]of m)f[p][h]&&(g+=f[p][h],v[p][h]=!0)}return g.replace(/X+$/,"")};return w.jsxs("div",{style:{backgroundColor:"#ffffff",border:"2px solid #e5e7eb",borderRadius:"16px",padding:"30px",boxShadow:"0 10px 25px rgba(0,0,0,0.1)"},children:[w.jsx("h2",{style:{fontSize:"28px",fontWeight:"bold",color:"#374151",marginBottom:"20px",textAlign:"center"},children:"�️🐝🐜 Bio-Inspired Cipher: Nature's Communication Secrets"}),w.jsxs("div",{style:{backgroundColor:"#f0f9ff",border:"1px solid #0ea5e9",borderRadius:"8px",padding:"15px",marginBottom:"20px",fontSize:"14px",textAlign:"center"},children:[w.jsx("strong",{children:"Inspired by Nature:"})," Spider web construction 🕷️ + Bee waggle dance 🐝 + Ant pheromone trails 🐜"]}),w.jsx(Mx,{isVisible:s,onToggle:()=>l(!s)}),w.jsxs("div",{style:{marginBottom:"20px"},children:[w.jsx("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold",color:"#374151"},children:"Enter your message:"}),w.jsx("input",{type:"text",value:e,onChange:u=>t(u.target.value),placeholder:"Enter your message here...",style:{width:"100%",padding:"12px 16px",border:"2px solid #d1d5db",borderRadius:"8px",fontSize:"16px",outline:"none",transition:"border-color 0.2s"},onFocus:u=>u.target.style.borderColor="#3b82f6",onBlur:u=>u.target.style.borderColor="#d1d5db"})]}),w.jsxs("div",{style:{marginBottom:"20px",textAlign:"center"},children:[w.jsx("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold",color:"#374151"},children:"🕸️ Spider Web Size (Grid Dimensions):"}),w.jsx("div",{style:{display:"flex",gap:"8px",justifyContent:"center",flexWrap:"wrap"},children:[3,4,5,6,7,8].map(u=>w.jsxs("button",{onClick:()=>o(u),style:{padding:"8px 16px",border:"2px solid",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:i===u?"#8b5cf6":"#f3f4f6",borderColor:i===u?"#7c3aed":"#d1d5db",color:i===u?"white":"#374151",transform:i===u?"scale(1.05)":"scale(1)"},children:[u,"×",u]},u))}),w.jsxs("p",{style:{fontSize:"12px",color:"#6b7280",marginTop:"8px"},children:["Larger grids can hold more characters (",i,"×",i," = ",i*i," characters)"]})]}),w.jsxs("div",{style:{display:"flex",gap:"12px",marginBottom:"30px",justifyContent:"center"},children:[w.jsx("button",{onClick:()=>r("encrypt"),style:{padding:"12px 24px",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"16px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:n==="encrypt"?"#f59e0b":"#fef3c7",color:n==="encrypt"?"white":"#92400e",transform:n==="encrypt"?"scale(1.05)":"scale(1)"},children:"🕷️ Encrypt"}),w.jsx("button",{onClick:()=>r("decrypt"),style:{padding:"12px 24px",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"16px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:n==="decrypt"?"#3b82f6":"#dbeafe",color:n==="decrypt"?"white":"#1e40af",transform:n==="decrypt"?"scale(1.05)":"scale(1)"},children:"🔓 Decrypt"})]}),n==="encrypt"?w.jsxs("div",{children:[w.jsx("h3",{style:{textAlign:"center",color:"#92400e",marginBottom:"10px"},children:"🕷️ Spider Web Construction + 🐝 Bee Dance + 🐜 Ant Trails"}),w.jsx(Ex,{message:e,gridSize:i}),e&&w.jsx("div",{style:{marginTop:"20px",padding:"15px",backgroundColor:"#fef3c7",border:"2px solid #f59e0b",borderRadius:"8px",textAlign:"center"},children:w.jsxs("strong",{style:{color:"#92400e"},children:["🔐 Bio-Encrypted: ",a()]})})]}):w.jsxs("div",{children:[w.jsx("h3",{style:{textAlign:"center",color:"#1e40af",marginBottom:"10px"},children:"🔍 Bio-Pattern Analysis & Decryption"}),w.jsx(Lx,{message:e,gridSize:i})]})]})}function Vx(){return w.jsx("div",{style:{minHeight:"100vh",backgroundColor:"#fffbee",padding:"20px"},children:w.jsxs("div",{style:{maxWidth:"800px",margin:"0 auto"},children:[w.jsx("h1",{style:{textAlign:"center",color:"#92400e",fontSize:"2.5rem",marginBottom:"1rem"},children:"🕷️🐝🐜 Nature's Cipher Laboratory"}),w.jsx("p",{style:{textAlign:"center",color:"#6b7280",fontSize:"1.2rem",marginBottom:"2rem",fontStyle:"italic"},children:"Encryption inspired by spider webs, bee dances, and ant trails"}),w.jsx(Ax,{})]})})}Ls.createRoot(document.getElementById("root")).render(w.jsx(Ol.StrictMode,{children:w.jsx(Vx,{})}));
